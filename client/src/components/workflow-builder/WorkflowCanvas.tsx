/**
 * Canvas del Workflow - Área principal de trabajo
 * Versión básica sin React Flow (se implementará después)
 */

import React, { useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Plus, Zap } from 'lucide-react';

import { VisualWorkflow, NodePosition } from '@/types/workflow-types';

interface WorkflowCanvasProps {
  workflow: VisualWorkflow;
  onNodeUpdate: (nodeId: string, updates: any) => void;
  onNodeDelete: (nodeId: string) => void;
  onNodeSelect: (nodeId: string | null) => void;
  selectedNode: string | null;
  isExecuting: boolean;
}

export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  workflow,
  onNodeUpdate,
  onNodeDelete,
  onNodeSelect,
  selectedNode,
  isExecuting
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    // Si se hace clic en el canvas (no en un nodo), deseleccionar
    if (event.target === event.currentTarget) {
      onNodeSelect(null);
    }
  }, [onNodeSelect]);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    
    const nodeType = event.dataTransfer.getData('application/reactflow');
    if (!nodeType || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const position: NodePosition = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    // TODO: Implementar adición de nodo desde drag & drop
    console.log('Drop node:', nodeType, 'at position:', position);
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  return (
    <div 
      ref={canvasRef}
      className="w-full h-full bg-gradient-to-br from-slate-900/50 to-purple-900/50 relative overflow-hidden"
      onClick={handleCanvasClick}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      {/* Grid Background */}
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      />

      {/* Nodes */}
      {workflow.nodes.map((node) => (
        <motion.div
          key={node.id}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.02 }}
          className={`
            absolute bg-black/40 backdrop-blur-sm border rounded-lg p-4 cursor-pointer
            transition-all duration-200 min-w-[200px]
            ${selectedNode === node.id 
              ? 'border-purple-400 shadow-lg shadow-purple-500/20' 
              : 'border-purple-500/30 hover:border-purple-400/50'
            }
            ${isExecuting ? 'animate-pulse' : ''}
          `}
          style={{
            left: node.position.x,
            top: node.position.y
          }}
          onClick={(e) => {
            e.stopPropagation();
            onNodeSelect(node.id);
          }}
        >
          {/* Node Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-purple-400" />
              <h3 className="text-sm font-medium text-white truncate">
                {node.data.nodeInfo?.display_name || node.data.class_type}
              </h3>
            </div>
            <div className={`w-2 h-2 rounded-full ${
              node.data.isValid ? 'bg-green-400' : 'bg-red-400'
            }`} />
          </div>

          {/* Node Content */}
          <div className="space-y-2">
            <p className="text-xs text-purple-200 line-clamp-2">
              {node.data.nodeInfo?.description || 'Nodo de procesamiento'}
            </p>
            
            {/* Input/Output indicators */}
            <div className="flex justify-between items-center">
              <div className="flex space-x-1">
                {node.data.nodeInfo?.input_types.required && 
                  Object.keys(node.data.nodeInfo.input_types.required).map((_, index) => (
                    <div key={index} className="w-2 h-2 bg-blue-400 rounded-full" />
                  ))
                }
              </div>
              <div className="flex space-x-1">
                {node.data.nodeInfo?.return_types.map((_, index) => (
                  <div key={index} className="w-2 h-2 bg-green-400 rounded-full" />
                ))}
              </div>
            </div>
          </div>

          {/* Execution indicator */}
          {isExecuting && (
            <div className="absolute -top-1 -right-1">
              <div className="w-3 h-3 bg-purple-500 rounded-full animate-ping" />
            </div>
          )}
        </motion.div>
      ))}

      {/* Connections (simplified) */}
      <svg className="absolute inset-0 pointer-events-none">
        {workflow.edges.map((edge) => {
          const sourceNode = workflow.nodes.find(n => n.id === edge.source);
          const targetNode = workflow.nodes.find(n => n.id === edge.target);
          
          if (!sourceNode || !targetNode) return null;

          const sourceX = sourceNode.position.x + 200; // Node width
          const sourceY = sourceNode.position.y + 30; // Node height / 2
          const targetX = targetNode.position.x;
          const targetY = targetNode.position.y + 30;

          return (
            <motion.line
              key={edge.id}
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              x1={sourceX}
              y1={sourceY}
              x2={targetX}
              y2={targetY}
              stroke="rgba(139, 92, 246, 0.6)"
              strokeWidth="2"
              strokeDasharray={isExecuting ? "5,5" : "none"}
              className={isExecuting ? "animate-pulse" : ""}
            />
          );
        })}
      </svg>

      {/* Empty State */}
      {workflow.nodes.length === 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plus className="h-8 w-8 text-purple-400" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              Comienza tu Workflow
            </h3>
            <p className="text-purple-200 mb-4 max-w-md">
              Arrastra nodos desde la biblioteca lateral o haz clic en ellos para comenzar a crear tu workflow de generación de imágenes AI.
            </p>
            <div className="space-y-2 text-sm text-purple-300">
              <p>💡 Conecta nodos para crear flujos de procesamiento</p>
              <p>⚡ Ejecuta workflows para generar imágenes</p>
              <p>🎨 Usa plantillas predefinidas para empezar rápido</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Canvas Info */}
      <div className="absolute bottom-4 right-4 bg-black/40 backdrop-blur-sm rounded-lg p-3 border border-purple-500/30">
        <div className="text-xs text-purple-200 space-y-1">
          <div>Nodos: {workflow.nodes.length}</div>
          <div>Conexiones: {workflow.edges.length}</div>
          {selectedNode && <div>Seleccionado: {selectedNode}</div>}
        </div>
      </div>
    </div>
  );
};
