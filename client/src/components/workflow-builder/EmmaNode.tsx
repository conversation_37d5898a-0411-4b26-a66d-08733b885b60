/**
 * Componente de nodo personalizado para Emma Workflows
 * Nodo visual con inputs/outputs y configuración
 */

import React, { memo, useState } from 'react';
import { <PERSON>le, Position, NodeProps } from 'reactflow';
import { motion } from 'framer-motion';
import { Settings, X, Check, AlertTriangle } from 'lucide-react';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { EmmaNodeData, InputConfig } from '@/types/workflow-types';
import { EMMA_NODES } from '@/data/emma-nodes';

interface EmmaNodeProps extends NodeProps {
  data: EmmaNodeData;
}

const EmmaNode: React.FC<EmmaNodeProps> = ({ id, data, selected }) => {
  const [showConfig, setShowConfig] = useState(false);
  const [localInputs, setLocalInputs] = useState(data.inputs || {});

  const nodeDefinition = EMMA_NODES[data.nodeType];
  if (!nodeDefinition) {
    return (
      <Card className="min-w-[200px] border-red-500 bg-red-500/10">
        <CardContent className="p-4">
          <div className="text-red-300 text-sm">Nodo desconocido: {data.nodeType}</div>
        </CardContent>
      </Card>
    );
  }

  const handleInputChange = (inputName: string, value: any) => {
    setLocalInputs(prev => ({
      ...prev,
      [inputName]: value
    }));
  };

  const saveConfiguration = () => {
    // TODO: Actualizar datos del nodo
    data.inputs = localInputs;
    setShowConfig(false);
  };

  const renderInputField = (inputName: string, config: InputConfig) => {
    const value = localInputs[inputName] || config.default || '';

    switch (config.type) {
      case 'text':
        if (config.options) {
          return (
            <Select value={value} onValueChange={(val) => handleInputChange(inputName, val)}>
              <SelectTrigger className="bg-black/20 border-purple-500/30">
                <SelectValue placeholder={config.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {config.options.map(option => (
                  <SelectItem key={option} value={option}>{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }
        
        if (config.placeholder?.includes('...') || inputName.includes('prompt')) {
          return (
            <Textarea
              value={value}
              onChange={(e) => handleInputChange(inputName, e.target.value)}
              placeholder={config.placeholder}
              className="bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
              rows={3}
            />
          );
        }
        
        return (
          <Input
            value={value}
            onChange={(e) => handleInputChange(inputName, e.target.value)}
            placeholder={config.placeholder}
            className="bg-black/20 border-purple-500/30 text-white placeholder-purple-300"
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleInputChange(inputName, parseFloat(e.target.value) || 0)}
            min={config.min}
            max={config.max}
            step={config.step}
            className="bg-black/20 border-purple-500/30 text-white"
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleInputChange(inputName, e.target.checked)}
              className="w-4 h-4 text-purple-600 bg-black/20 border-purple-500/30 rounded focus:ring-purple-500"
            />
            <span className="text-sm text-purple-200">
              {value ? 'Activado' : 'Desactivado'}
            </span>
          </div>
        );

      default:
        return (
          <div className="text-xs text-purple-300 p-2 bg-purple-500/10 rounded">
            Conexión requerida: {config.type}
          </div>
        );
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.02 }}
        className={`relative ${selected ? 'ring-2 ring-purple-400' : ''}`}
      >
        <Card 
          className={`min-w-[250px] border transition-all duration-200 ${
            selected 
              ? 'border-purple-400 shadow-lg shadow-purple-500/20' 
              : 'border-purple-500/30 hover:border-purple-400/50'
          } ${
            data.isExecuting 
              ? 'bg-yellow-500/10 border-yellow-400' 
              : data.isCompleted 
                ? 'bg-green-500/10 border-green-400'
                : 'bg-black/40'
          }`}
          style={{ backgroundColor: `${nodeDefinition.color}15` }}
        >
          {/* Handles de entrada */}
          {Object.keys(nodeDefinition.inputs).map((inputName, index) => (
            <Handle
              key={`input-${inputName}`}
              type="target"
              position={Position.Left}
              id={inputName}
              style={{
                top: 60 + index * 20,
                background: '#8b5cf6',
                border: '2px solid #1f2937',
                width: 12,
                height: 12
              }}
            />
          ))}

          {/* Handles de salida */}
          {Object.keys(nodeDefinition.outputs).map((outputName, index) => (
            <Handle
              key={`output-${outputName}`}
              type="source"
              position={Position.Right}
              id={outputName}
              style={{
                top: 60 + index * 20,
                background: '#10b981',
                border: '2px solid #1f2937',
                width: 12,
                height: 12
              }}
            />
          ))}

          <CardHeader className="p-3 pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{nodeDefinition.icon}</span>
                <div>
                  <h3 className="text-sm font-medium text-white">{nodeDefinition.name}</h3>
                  <p className="text-xs text-purple-200">{nodeDefinition.category}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                {data.isExecuting && (
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
                )}
                {data.isCompleted && (
                  <Check className="w-4 h-4 text-green-400" />
                )}
                {data.errors.length > 0 && (
                  <AlertTriangle className="w-4 h-4 text-red-400" />
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowConfig(!showConfig)}
                  className="w-6 h-6 p-0 text-purple-300 hover:bg-purple-500/20"
                >
                  <Settings className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-3 pt-0">
            <p className="text-xs text-purple-200 mb-3">{nodeDefinition.description}</p>
            
            {/* Inputs básicos */}
            {Object.entries(nodeDefinition.inputs).map(([inputName, config]) => (
              <div key={inputName} className="mb-2">
                <div className="flex items-center space-x-2 text-xs">
                  <div className="w-2 h-2 bg-purple-400 rounded-full" />
                  <span className="text-purple-200">{config.label}</span>
                  {config.required && <span className="text-red-400">*</span>}
                </div>
              </div>
            ))}

            {/* Outputs */}
            <div className="mt-3 pt-2 border-t border-purple-500/20">
              {Object.entries(nodeDefinition.outputs).map(([outputName, config]) => (
                <div key={outputName} className="flex items-center justify-end space-x-2 text-xs mb-1">
                  <span className="text-purple-200">{config.label}</span>
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Indicador de ejecución */}
        {data.isExecuting && (
          <div className="absolute -top-2 -right-2">
            <div className="w-4 h-4 bg-yellow-500 rounded-full animate-ping" />
          </div>
        )}
      </motion.div>

      {/* Panel de configuración */}
      {showConfig && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
          onClick={() => setShowConfig(false)}
        >
          <Card 
            className="bg-black/80 backdrop-blur-sm border-purple-500/30 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-white">
                  {nodeDefinition.icon} {nodeDefinition.name}
                </h3>
                <p className="text-sm text-purple-200">{nodeDefinition.description}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowConfig(false)}
                className="text-purple-300 hover:bg-purple-500/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </CardHeader>

            <CardContent className="space-y-4">
              {Object.entries(nodeDefinition.inputs).map(([inputName, config]) => (
                <div key={inputName} className="space-y-2">
                  <Label className="text-sm font-medium text-purple-200 flex items-center space-x-2">
                    <span>{config.label}</span>
                    {config.required && <span className="text-red-400">*</span>}
                  </Label>
                  {renderInputField(inputName, config)}
                  {config.tooltip && (
                    <p className="text-xs text-purple-300">{config.tooltip}</p>
                  )}
                </div>
              ))}

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowConfig(false)}
                  className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={saveConfiguration}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                >
                  Guardar
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </>
  );
};

export default memo(EmmaNode);
