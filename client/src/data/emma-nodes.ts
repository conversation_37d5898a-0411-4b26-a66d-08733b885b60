/**
 * Definiciones de nodos Emma para el Workflow Builder
 * Sistema simplificado y fácil de usar
 */

import { EmmaNodeDefinition } from '@/types/workflow-types';

// 📝 NODOS DE ENTRADA
export const textInputNode: EmmaNodeDefinition = {
  id: 'text-input',
  name: 'Entrada de Texto',
  category: 'Entrada',
  description: 'Ingresa texto o prompts para generar contenido',
  icon: '📝',
  color: '#10b981',
  inputs: {},
  outputs: {
    text: { type: 'text', label: 'Texto' }
  }
};

export const imageInputNode: EmmaNodeDefinition = {
  id: 'image-input',
  name: 'Subir Imagen',
  category: 'Entrada',
  description: 'Sube una imagen para procesar',
  icon: '🖼️',
  color: '#10b981',
  inputs: {},
  outputs: {
    image: { type: 'image', label: 'Imagen' }
  }
};

// 🎨 NODOS DE GENERACIÓN DE IMÁGENES
export const ideogramGeneratorNode: EmmaNodeDefinition = {
  id: 'ideogram-generator',
  name: 'Ideogram AI',
  category: 'Generación',
  description: 'Genera imágenes con Ideogram AI',
  icon: '🎨',
  color: '#3b82f6',
  provider: 'ideogram',
  endpoint: '/api/image-generator/generate',
  inputs: {
    prompt: { 
      type: 'text', 
      label: 'Prompt', 
      required: true,
      placeholder: 'Describe la imagen que quieres generar...'
    },
    model: { 
      type: 'text', 
      label: 'Modelo', 
      default: 'ideogram-3.0',
      options: ['ideogram-3.0', 'ideogram-2.0']
    },
    aspect_ratio: { 
      type: 'text', 
      label: 'Aspecto', 
      default: '1:1',
      options: ['1:1', '16:9', '9:16', '4:3', '3:4']
    },
    style: { 
      type: 'text', 
      label: 'Estilo', 
      default: 'auto',
      options: ['auto', 'realistic', 'design', 'anime', '3d']
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen' },
    url: { type: 'url', label: 'URL' }
  }
};

export const dalleGeneratorNode: EmmaNodeDefinition = {
  id: 'dalle-generator',
  name: 'DALL-E 3',
  category: 'Generación',
  description: 'Genera imágenes con OpenAI DALL-E',
  icon: '🤖',
  color: '#8b5cf6',
  provider: 'openai',
  endpoint: '/api/v1/openai-images/generate',
  inputs: {
    prompt: { 
      type: 'text', 
      label: 'Prompt', 
      required: true,
      placeholder: 'Describe la imagen...'
    },
    model: { 
      type: 'text', 
      label: 'Modelo', 
      default: 'dall-e-3',
      options: ['dall-e-3', 'dall-e-2']
    },
    size: { 
      type: 'text', 
      label: 'Tamaño', 
      default: '1024x1024',
      options: ['1024x1024', '1792x1024', '1024x1792']
    },
    quality: { 
      type: 'text', 
      label: 'Calidad', 
      default: 'standard',
      options: ['standard', 'hd']
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen' },
    url: { type: 'url', label: 'URL' }
  }
};

// ✨ NODOS DE EDICIÓN
export const upscaleNode: EmmaNodeDefinition = {
  id: 'upscale',
  name: 'Mejorar Imagen',
  category: 'Edición',
  description: 'Mejora la resolución y calidad de la imagen',
  icon: '⬆️',
  color: '#f59e0b',
  provider: 'stability',
  endpoint: '/api/v1/ai-editor/upscale',
  inputs: {
    image: { type: 'image', label: 'Imagen', required: true },
    scale_factor: { 
      type: 'number', 
      label: 'Factor', 
      default: 2,
      options: ['2', '4']
    },
    creativity: { 
      type: 'number', 
      label: 'Creatividad', 
      default: 0.3,
      min: 0,
      max: 1,
      step: 0.1
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen Mejorada' },
    url: { type: 'url', label: 'URL' }
  }
};

export const backgroundRemoverNode: EmmaNodeDefinition = {
  id: 'background-remover',
  name: 'Remover Fondo',
  category: 'Edición',
  description: 'Remueve el fondo de la imagen automáticamente',
  icon: '🗑️',
  color: '#ef4444',
  provider: 'stability',
  endpoint: '/api/stability-remove-bg/remove-background',
  inputs: {
    image: { type: 'image', label: 'Imagen', required: true }
  },
  outputs: {
    image: { type: 'image', label: 'Sin Fondo' },
    url: { type: 'url', label: 'URL' }
  }
};

export const styleTransferNode: EmmaNodeDefinition = {
  id: 'style-transfer',
  name: 'Transferir Estilo',
  category: 'Edición',
  description: 'Aplica el estilo de una imagen a otra',
  icon: '🎭',
  color: '#ec4899',
  provider: 'emma',
  endpoint: '/api/v1/images/style-transfer',
  inputs: {
    content_image: { type: 'image', label: 'Imagen Base', required: true },
    style_image: { type: 'image', label: 'Imagen de Estilo', required: true },
    strength: { 
      type: 'number', 
      label: 'Intensidad', 
      default: 0.7,
      min: 0.1,
      max: 1.0,
      step: 0.1
    }
  },
  outputs: {
    image: { type: 'image', label: 'Imagen Estilizada' },
    url: { type: 'url', label: 'URL' }
  }
};

// 🎬 NODOS DE VIDEO
export const videoGeneratorNode: EmmaNodeDefinition = {
  id: 'video-generator',
  name: 'Generar Video',
  category: 'Video',
  description: 'Genera videos con Luma Labs',
  icon: '🎬',
  color: '#06b6d4',
  provider: 'luma',
  endpoint: '/api/luma-labs/generate',
  inputs: {
    prompt: { 
      type: 'text', 
      label: 'Prompt de Video', 
      required: true,
      placeholder: 'Describe el movimiento del video...'
    },
    reference_image: { type: 'image', label: 'Imagen de Referencia' },
    duration: { 
      type: 'text', 
      label: 'Duración', 
      default: '5',
      options: ['5', '10', '15']
    },
    aspect_ratio: { 
      type: 'text', 
      label: 'Aspecto', 
      default: '16:9',
      options: ['16:9', '9:16', '1:1']
    }
  },
  outputs: {
    video: { type: 'video', label: 'Video' },
    url: { type: 'url', label: 'URL' }
  }
};

// 💾 NODOS DE SALIDA
export const imageOutputNode: EmmaNodeDefinition = {
  id: 'image-output',
  name: 'Guardar Imagen',
  category: 'Salida',
  description: 'Guarda la imagen final',
  icon: '💾',
  color: '#84cc16',
  inputs: {
    image: { type: 'image', label: 'Imagen', required: true },
    filename: { 
      type: 'text', 
      label: 'Nombre', 
      default: 'emma-workflow',
      placeholder: 'nombre-archivo'
    },
    format: { 
      type: 'text', 
      label: 'Formato', 
      default: 'PNG',
      options: ['PNG', 'JPEG', 'WEBP']
    }
  },
  outputs: {
    url: { type: 'url', label: 'URL Final' }
  }
};

export const videoOutputNode: EmmaNodeDefinition = {
  id: 'video-output',
  name: 'Guardar Video',
  category: 'Salida',
  description: 'Guarda el video final',
  icon: '🎥',
  color: '#84cc16',
  inputs: {
    video: { type: 'video', label: 'Video', required: true },
    filename: { 
      type: 'text', 
      label: 'Nombre', 
      default: 'emma-video',
      placeholder: 'nombre-video'
    }
  },
  outputs: {
    url: { type: 'url', label: 'URL Final' }
  }
};

// Exportar todos los nodos
export const EMMA_NODES: Record<string, EmmaNodeDefinition> = {
  'text-input': textInputNode,
  'image-input': imageInputNode,
  'ideogram-generator': ideogramGeneratorNode,
  'dalle-generator': dalleGeneratorNode,
  'upscale': upscaleNode,
  'background-remover': backgroundRemoverNode,
  'style-transfer': styleTransferNode,
  'video-generator': videoGeneratorNode,
  'image-output': imageOutputNode,
  'video-output': videoOutputNode
};

// Categorías de nodos
export const NODE_CATEGORIES = {
  'Entrada': ['text-input', 'image-input'],
  'Generación': ['ideogram-generator', 'dalle-generator'],
  'Edición': ['upscale', 'background-remover', 'style-transfer'],
  'Video': ['video-generator'],
  'Salida': ['image-output', 'video-output']
};
