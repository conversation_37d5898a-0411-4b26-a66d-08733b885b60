/**
 * Emma Visual Workflow Builder - Sistema simplificado de workflows visuales
 * Drag & Drop con React Flow para crear campañas de contenido AI
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';

import {
  Play,
  Save,
  Download,
  Upload,
  Zap,
  Plus,
  Settings,
  Trash2
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { EmmaWorkflow, EmmaWorkflowNode, EmmaWorkflowEdge } from '@/types/workflow-types';
import { EMMA_NODES, NODE_CATEGORIES } from '@/data/emma-nodes';
import EmmaNode from '@/components/workflow-builder/EmmaNode';

// Tipos de nodos personalizados
const nodeTypes = {
  emmaNode: EmmaNode,
};

// Nodos iniciales de ejemplo
const initialNodes: Node[] = [];
const initialEdges: Edge[] = [];

const AIWorkflowBuilderPage: React.FC = () => {
  // Estados del workflow
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [showNodePanel, setShowNodePanel] = useState(true);

  // Handlers del workflow
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id);
  }, []);

  const addNode = useCallback((nodeType: string) => {
    const nodeDefinition = EMMA_NODES[nodeType];
    if (!nodeDefinition) return;

    const newNode: Node = {
      id: `${nodeType}-${Date.now()}`,
      type: 'emmaNode',
      position: {
        x: Math.random() * 400 + 100,
        y: Math.random() * 400 + 100
      },
      data: {
        nodeType,
        definition: nodeDefinition,
        inputs: {},
        outputs: {},
        isValid: true,
        errors: [],
        isExecuting: false,
        isCompleted: false
      },
    };

    setNodes((nds) => nds.concat(newNode));
  }, [setNodes]);

  const executeWorkflow = useCallback(async () => {
    setIsExecuting(true);

    try {
      // Convertir workflow a formato de ejecución
      const workflow = {
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.data.nodeType || 'unknown',
          inputs: node.data.inputs || {},
          position: node.position
        })),
        edges: edges.map(edge => ({
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        }))
      };

      // Ejecutar workflow
      const response = await fetch('/api/v1/emma-workflows/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ workflow })
      });

      const result = await response.json();
      console.log('Workflow ejecutado:', result);

    } catch (error) {
      console.error('Error ejecutando workflow:', error);
    } finally {
      setIsExecuting(false);
    }
  }, [nodes, edges]);

  const saveWorkflow = useCallback(() => {
    const workflow = {
      id: `workflow-${Date.now()}`,
      name: 'Mi Workflow',
      description: 'Workflow creado con Emma',
      nodes,
      edges,
      metadata: {
        version: '1.0.0',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        tags: [],
        category: 'general'
      }
    };

    // Guardar en localStorage por ahora
    localStorage.setItem('emma-workflow', JSON.stringify(workflow));
    console.log('Workflow guardado:', workflow);
  }, [nodes, edges]);

  // Componente de panel de nodos
  const NodePanel = () => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="w-80 bg-black/20 backdrop-blur-sm border-r border-purple-500/20 p-4"
    >
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Plus className="h-5 w-5 mr-2 text-purple-400" />
        Agregar Nodos
      </h3>

      {Object.entries(NODE_CATEGORIES).map(([category, nodeIds]) => (
        <div key={category} className="mb-6">
          <h4 className="text-sm font-medium text-purple-200 mb-3">{category}</h4>
          <div className="space-y-2">
            {nodeIds.map(nodeId => {
              const node = EMMA_NODES[nodeId];
              return (
                <button
                  key={nodeId}
                  onClick={() => addNode(nodeId)}
                  className="w-full p-3 bg-purple-500/10 hover:bg-purple-500/20 border border-purple-500/30 rounded-lg text-left transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{node.icon}</span>
                    <div>
                      <div className="text-sm font-medium text-white">{node.name}</div>
                      <div className="text-xs text-purple-200">{node.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      ))}
    </motion.div>
  );

  return (
    <ReactFlowProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-8 w-8 text-purple-400" />
                <h1 className="text-2xl font-bold text-white">Emma Visual Workflows</h1>
              </div>
              <Badge variant="outline" className="border-purple-400 text-purple-300">
                Drag & Drop Builder
              </Badge>
              <Badge variant="outline" className="border-green-400 text-green-300">
                {nodes.length} Nodos
              </Badge>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={saveWorkflow}
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
              >
                <Save className="h-4 w-4 mr-2" />
                Guardar
              </Button>

              <Button
                onClick={executeWorkflow}
                disabled={isExecuting || nodes.length === 0}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                {isExecuting ? (
                  <>
                    <Settings className="h-4 w-4 mr-2 animate-spin" />
                    Ejecutando...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Ejecutar
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowNodePanel(!showNodePanel)}
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Main Content */}
        <div className="flex h-[calc(100vh-120px)]">
          {/* Node Panel */}
          {showNodePanel && <NodePanel />}

          {/* Workflow Canvas */}
          <div className="flex-1 relative">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              fitView
              className="bg-transparent"
            >
              <Background
                variant={BackgroundVariant.Dots}
                gap={20}
                size={1}
                color="rgba(139, 92, 246, 0.2)"
              />
              <Controls
                className="bg-black/20 backdrop-blur-sm border border-purple-500/30"
              />
              <MiniMap
                className="bg-black/20 backdrop-blur-sm border border-purple-500/30"
                nodeColor="#8b5cf6"
                maskColor="rgba(0, 0, 0, 0.2)"
              />
            </ReactFlow>

            {/* Empty State */}
            {nodes.length === 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute inset-0 flex items-center justify-center pointer-events-none"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Zap className="h-8 w-8 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Crea tu primer workflow
                  </h3>
                  <p className="text-purple-200 mb-4 max-w-md">
                    Arrastra nodos desde el panel lateral para comenzar a construir tu flujo de trabajo de generación de contenido AI.
                  </p>
                  <div className="space-y-2 text-sm text-purple-300">
                    <p>🎨 Genera imágenes con Ideogram y DALL-E</p>
                    <p>✨ Edita y mejora con Stability AI</p>
                    <p>🎬 Crea videos con Luma Labs</p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </ReactFlowProvider>
  );
};

export default AIWorkflowBuilderPage;
