/**
 * AI Workflow Builder - Sistema de workflows visuales para Emma Studio
 * Basado en ComfyUI con interfaz React Flow
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Save, 
  Upload, 
  Download, 
  Trash2, 
  Settings, 
  Layers,
  Zap,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { 
  WorkflowEditorState,
  VisualWorkflow,
  WorkflowNode,
  WorkflowConnection,
  NodePosition
} from '@/types/workflow-types';
import { workflowService } from '@/services/workflow-service';

// Componentes del workflow builder (se crearán después)
import { WorkflowCanvas } from '@/components/workflow-builder/WorkflowCanvas';
import { NodeLibrary } from '@/components/workflow-builder/NodeLibrary';
import { NodeProperties } from '@/components/workflow-builder/NodeProperties';
import { WorkflowToolbar } from '@/components/workflow-builder/WorkflowToolbar';

const AIWorkflowBuilderPage: React.FC = () => {
  // Estado principal del editor
  const [editorState, setEditorState] = useState<WorkflowEditorState>({
    workflow: {
      nodes: [],
      edges: [],
      metadata: {
        name: 'Nuevo Workflow',
        description: 'Workflow de generación de imágenes AI',
        version: '1.0.0',
        created_at: new Date().toISOString()
      }
    },
    selectedNode: null,
    isExecuting: false,
    executionId: null,
    lastExecution: null,
    availableNodes: {},
    nodeCategories: {},
    templates: {},
    isLoading: true,
    error: null
  });

  // Estados locales
  const [showNodeLibrary, setShowNodeLibrary] = useState(true);
  const [showProperties, setShowProperties] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Inicialización
  useEffect(() => {
    initializeWorkflowBuilder();
  }, []);

  const initializeWorkflowBuilder = async () => {
    try {
      setEditorState(prev => ({ ...prev, isLoading: true, error: null }));

      // Verificar estado del servicio
      const health = await workflowService.healthCheck();
      if (!health.comfy_available) {
        throw new Error('ComfyUI no está disponible');
      }

      // Cargar datos necesarios
      const [availableNodes, nodeCategories, templates] = await Promise.all([
        workflowService.getAvailableNodes(),
        workflowService.getNodeCategories(),
        workflowService.getWorkflowTemplates()
      ]);

      setEditorState(prev => ({
        ...prev,
        availableNodes,
        nodeCategories,
        templates,
        isLoading: false
      }));

    } catch (error) {
      console.error('Error initializing workflow builder:', error);
      setEditorState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Error desconocido',
        isLoading: false
      }));
    }
  };

  // Handlers para el workflow
  const handleNodeAdd = useCallback((nodeType: string, position: NodePosition) => {
    const nodeInfo = editorState.availableNodes[nodeType];
    if (!nodeInfo) return;

    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      type: 'customNode',
      position,
      data: {
        id: `node_${Date.now()}`,
        class_type: nodeType,
        inputs: {},
        nodeInfo,
        isValid: true,
        errors: []
      }
    };

    setEditorState(prev => ({
      ...prev,
      workflow: {
        ...prev.workflow,
        nodes: [...prev.workflow.nodes, newNode]
      }
    }));
  }, [editorState.availableNodes]);

  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<any>) => {
    setEditorState(prev => ({
      ...prev,
      workflow: {
        ...prev.workflow,
        nodes: prev.workflow.nodes.map(node =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, ...updates } }
            : node
        )
      }
    }));
  }, []);

  const handleNodeDelete = useCallback((nodeId: string) => {
    setEditorState(prev => ({
      ...prev,
      workflow: {
        ...prev.workflow,
        nodes: prev.workflow.nodes.filter(node => node.id !== nodeId),
        edges: prev.workflow.edges.filter(edge => 
          edge.source !== nodeId && edge.target !== nodeId
        )
      },
      selectedNode: prev.selectedNode === nodeId ? null : prev.selectedNode
    }));
  }, []);

  const handleNodeSelect = useCallback((nodeId: string | null) => {
    setEditorState(prev => ({ ...prev, selectedNode: nodeId }));
    setShowProperties(nodeId !== null);
  }, []);

  const handleWorkflowExecute = useCallback(async () => {
    try {
      setEditorState(prev => ({ ...prev, isExecuting: true, error: null }));

      // Convertir workflow visual a formato ComfyUI
      const comfyWorkflow = await workflowService.convertVisualToComfy(editorState.workflow);

      // Validar workflow
      const validation = await workflowService.validateWorkflow(comfyWorkflow);
      if (!validation.valid) {
        throw new Error(`Workflow inválido: ${validation.errors.join(', ')}`);
      }

      // Ejecutar workflow
      const execution = await workflowService.executeWorkflow(comfyWorkflow);

      setEditorState(prev => ({
        ...prev,
        isExecuting: false,
        executionId: execution.execution_id,
        lastExecution: execution
      }));

    } catch (error) {
      console.error('Error executing workflow:', error);
      setEditorState(prev => ({
        ...prev,
        isExecuting: false,
        error: error instanceof Error ? error.message : 'Error ejecutando workflow'
      }));
    }
  }, [editorState.workflow]);

  const handleWorkflowSave = useCallback(() => {
    const name = editorState.workflow.metadata.name || 'Workflow sin nombre';
    workflowService.saveWorkflowLocally(name, editorState.workflow);
    // TODO: Mostrar toast de confirmación
  }, [editorState.workflow]);

  const handleWorkflowExport = useCallback(() => {
    const filename = `${editorState.workflow.metadata.name || 'workflow'}_${Date.now()}.json`;
    workflowService.exportWorkflow(editorState.workflow, filename);
  }, [editorState.workflow]);

  const selectedNode = editorState.workflow.nodes.find(node => node.id === editorState.selectedNode);

  if (editorState.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2">Cargando Emma AI Workflows</h2>
          <p className="text-purple-200">Inicializando sistema de workflows visuales...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Zap className="h-8 w-8 text-purple-400" />
              <h1 className="text-2xl font-bold text-white">Emma AI Workflows</h1>
            </div>
            <Badge variant="outline" className="border-purple-400 text-purple-300">
              ComfyUI Powered
            </Badge>
          </div>

          <WorkflowToolbar
            onSave={handleWorkflowSave}
            onLoad={() => {}} // TODO: Implementar
            onExecute={handleWorkflowExecute}
            onClear={() => {}} // TODO: Implementar
            onExport={handleWorkflowExport}
            onImport={() => {}} // TODO: Implementar
            isExecuting={editorState.isExecuting}
            canExecute={editorState.workflow.nodes.length > 0}
          />
        </div>
      </motion.header>

      {/* Error Alert */}
      {editorState.error && (
        <Alert className="m-4 border-red-500 bg-red-500/10">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-red-300">
            {editorState.error}
          </AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {editorState.lastExecution?.success && (
        <Alert className="m-4 border-green-500 bg-green-500/10">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-green-300">
            Workflow ejecutado exitosamente: {editorState.lastExecution.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <div className="flex h-[calc(100vh-120px)]">
        {/* Node Library Sidebar */}
        {showNodeLibrary && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="w-80 bg-black/20 backdrop-blur-sm border-r border-purple-500/20"
          >
            <NodeLibrary
              categories={editorState.nodeCategories}
              onNodeAdd={handleNodeAdd}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
            />
          </motion.div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 relative">
          <WorkflowCanvas
            workflow={editorState.workflow}
            onNodeUpdate={handleNodeUpdate}
            onNodeDelete={handleNodeDelete}
            onNodeSelect={handleNodeSelect}
            selectedNode={editorState.selectedNode}
            isExecuting={editorState.isExecuting}
          />

          {/* Toggle Buttons */}
          <div className="absolute top-4 left-4 flex flex-col space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowNodeLibrary(!showNodeLibrary)}
              className="bg-black/20 backdrop-blur-sm border-purple-500/30"
            >
              <Layers className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Properties Panel */}
        {showProperties && selectedNode && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="w-80 bg-black/20 backdrop-blur-sm border-l border-purple-500/20"
          >
            <NodeProperties
              node={selectedNode}
              onUpdate={(updates) => handleNodeUpdate(selectedNode.id, updates)}
              onClose={() => setShowProperties(false)}
            />
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AIWorkflowBuilderPage;
