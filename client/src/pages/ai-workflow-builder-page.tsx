/**
 * Emma AI Workflow Builder - ComfyUI integrado con branding Emma
 * Usa ComfyUI completo con interfaz Emma encima
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  ExternalLink,
  Zap,
  AlertCircle,
  CheckCircle,
  Power,
  PowerOff,
  Loader2,
  Monitor
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ComfyUIStatus {
  success: boolean;
  comfy_available: boolean;
  comfy_running: boolean;
  comfy_url: string;
  message: string;
}

const AIWorkflowBuilderPage: React.FC = () => {
  // Estados
  const [comfyStatus, setComfyStatus] = useState<ComfyUIStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isStarting, setIsStarting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showComfyUI, setShowComfyUI] = useState(false);

  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Inicialización
  useEffect(() => {
    checkComfyUIStatus();
  }, []);

  const checkComfyUIStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/v1/comfy-workflows/health');
      const status: ComfyUIStatus = await response.json();

      setComfyStatus(status);

      if (status.comfy_running) {
        setShowComfyUI(true);
      }

    } catch (error) {
      console.error('Error checking ComfyUI status:', error);
      setError('Error verificando estado de ComfyUI');
    } finally {
      setIsLoading(false);
    }
  };

  const startComfyUI = async () => {
    try {
      setIsStarting(true);
      setError(null);

      const response = await fetch('/api/v1/comfy-workflows/start', {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Error iniciando ComfyUI');
      }

      // Esperar un poco y verificar estado
      setTimeout(() => {
        checkComfyUIStatus();
        setIsStarting(false);
      }, 3000);

    } catch (error) {
      console.error('Error starting ComfyUI:', error);
      setError('Error iniciando ComfyUI');
      setIsStarting(false);
    }
  };

  const stopComfyUI = async () => {
    try {
      await fetch('/api/v1/comfy-workflows/stop', {
        method: 'POST'
      });

      setShowComfyUI(false);
      checkComfyUIStatus();

    } catch (error) {
      console.error('Error stopping ComfyUI:', error);
      setError('Error deteniendo ComfyUI');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2">Cargando Emma AI Workflows</h2>
          <p className="text-purple-200">Verificando estado de ComfyUI...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Zap className="h-8 w-8 text-purple-400" />
              <h1 className="text-2xl font-bold text-white">Emma AI Workflows</h1>
            </div>
            <Badge variant="outline" className="border-purple-400 text-purple-300">
              ComfyUI Powered
            </Badge>
            {comfyStatus && (
              <Badge
                variant="outline"
                className={`${
                  comfyStatus.comfy_running
                    ? 'border-green-400 text-green-300'
                    : 'border-red-400 text-red-300'
                }`}
              >
                {comfyStatus.comfy_running ? 'Activo' : 'Inactivo'}
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {comfyStatus?.comfy_running ? (
              <>
                <Button
                  variant="outline"
                  onClick={stopComfyUI}
                  className="border-red-500/30 text-red-300 hover:bg-red-500/10"
                >
                  <PowerOff className="h-4 w-4 mr-2" />
                  Detener ComfyUI
                </Button>
                <Button
                  onClick={() => setShowComfyUI(!showComfyUI)}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                >
                  <Monitor className="h-4 w-4 mr-2" />
                  {showComfyUI ? 'Ocultar' : 'Mostrar'} ComfyUI
                </Button>
              </>
            ) : (
              <Button
                onClick={startComfyUI}
                disabled={isStarting}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
              >
                {isStarting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Iniciando...
                  </>
                ) : (
                  <>
                    <Power className="h-4 w-4 mr-2" />
                    Iniciar ComfyUI
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </motion.header>

      {/* Error Alert */}
      {error && (
        <Alert className="m-4 border-red-500 bg-red-500/10">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-red-300">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <div className="h-[calc(100vh-120px)]">
        {showComfyUI && comfyStatus?.comfy_running ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="h-full"
          >
            <iframe
              ref={iframeRef}
              src={comfyStatus.comfy_url}
              className="w-full h-full border-0"
              title="ComfyUI Interface"
              style={{
                background: 'linear-gradient(135deg, #1e293b 0%, #7c3aed 100%)'
              }}
            />
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="h-full flex items-center justify-center"
          >
            <Card className="bg-black/40 backdrop-blur-sm border-purple-500/30 max-w-2xl mx-auto">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-white flex items-center justify-center space-x-2">
                  <Zap className="h-8 w-8 text-purple-400" />
                  <span>Emma AI Workflows</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center space-y-6">
                <p className="text-purple-200 text-lg">
                  Sistema de workflows visuales de generación de imágenes AI
                </p>

                {comfyStatus ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="bg-purple-500/10 p-3 rounded-lg">
                        <p className="text-purple-300">ComfyUI Disponible</p>
                        <p className={`font-semibold ${
                          comfyStatus.comfy_available ? 'text-green-300' : 'text-red-300'
                        }`}>
                          {comfyStatus.comfy_available ? 'Sí' : 'No'}
                        </p>
                      </div>
                      <div className="bg-purple-500/10 p-3 rounded-lg">
                        <p className="text-purple-300">Estado del Servidor</p>
                        <p className={`font-semibold ${
                          comfyStatus.comfy_running ? 'text-green-300' : 'text-red-300'
                        }`}>
                          {comfyStatus.comfy_running ? 'Corriendo' : 'Detenido'}
                        </p>
                      </div>
                    </div>

                    {!comfyStatus.comfy_running && (
                      <div className="space-y-3">
                        <p className="text-purple-200">
                          Inicia ComfyUI para comenzar a crear workflows visuales
                        </p>
                        <Button
                          onClick={startComfyUI}
                          disabled={isStarting}
                          size="lg"
                          className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                        >
                          {isStarting ? (
                            <>
                              <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                              Iniciando ComfyUI...
                            </>
                          ) : (
                            <>
                              <Power className="h-5 w-5 mr-2" />
                              Iniciar ComfyUI
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
                    <p className="text-purple-200">Verificando estado de ComfyUI...</p>
                  </div>
                )}

                <div className="space-y-2 text-sm text-purple-300 border-t border-purple-500/20 pt-4">
                  <p>💡 Crea workflows visuales arrastrando y conectando nodos</p>
                  <p>⚡ Genera imágenes con Stable Diffusion y otros modelos AI</p>
                  <p>🎨 Usa ControlNet, LoRA, y técnicas avanzadas de generación</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AIWorkflowBuilderPage;
