"""
Emma Background Remover Node - Usa la API de Stability AI existente de Emma
"""

import requests
import logging
import base64
import io
from typing import Tuple

class EmmaBackgroundRemover:
    """Nodo que usa la API de Background Removal de Emma Studio"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "image": ("IMAGE", {
                    "tooltip": "Imagen para remover el fondo"
                }),
            }
        }
    
    RETURN_TYPES = ("IMAGE", "STRING")
    RETURN_NAMES = ("image_no_bg", "image_url")
    FUNCTION = "remove_background"
    CATEGORY = "Emma Studio/Image Editing"
    DESCRIPTION = "Remueve el fondo de imágenes usando Stability AI de Emma Studio"
    
    def remove_background(self, image):
        """Remueve fondo usando la API de Stability AI existente"""
        try:
            # Convertir tensor de ComfyUI a imagen PIL
            import torch
            import numpy as np
            from PIL import Image
            
            # Convertir de tensor a PIL
            if len(image.shape) == 4:
                image_array = image[0].cpu().numpy()
            else:
                image_array = image.cpu().numpy()
            
            image_array = (image_array * 255).astype(np.uint8)
            pil_image = Image.fromarray(image_array)
            
            # Convertir a base64 para la API
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Preparar datos para la API
            api_data = {
                "image": image_base64
            }
            
            # Llamar a la API existente de Emma
            response = requests.post(
                "http://localhost:8001/api/stability-remove-bg/remove-background",
                json=api_data,
                timeout=60
            )
            
            if response.status_code != 200:
                raise Exception(f"Error en API Background Removal: {response.status_code}")
            
            result = response.json()
            
            if not result.get("success"):
                raise Exception(f"Error removiendo fondo: {result.get('error', 'Unknown error')}")
            
            # Obtener imagen sin fondo
            no_bg_url = result.get("no_background_url")
            if not no_bg_url:
                raise Exception("No se recibió URL de imagen sin fondo")
            
            # Descargar imagen sin fondo
            image_response = requests.get(no_bg_url, timeout=30)
            image_response.raise_for_status()
            
            # Convertir a tensor de ComfyUI
            no_bg_image = Image.open(io.BytesIO(image_response.content))
            no_bg_image = no_bg_image.convert("RGBA")  # Mantener transparencia
            
            image_array = np.array(no_bg_image).astype(np.float32) / 255.0
            image_tensor = torch.from_numpy(image_array)[None,]
            
            logging.info(f"Fondo removido exitosamente: {no_bg_url}")
            
            return (image_tensor, no_bg_url)
            
        except Exception as e:
            logging.error(f"Error en EmmaBackgroundRemover: {str(e)}")
            # Retornar imagen original en caso de error
            return (image, "")
    
    def _create_error_image(self, error_message):
        """Crea una imagen de error"""
        from PIL import Image, ImageDraw, ImageFont
        import torch
        import numpy as np
        
        img = Image.new('RGBA', (512, 512), color=(255, 0, 255, 255))
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"BG Remove Error: {error_message[:40]}..."
        draw.text((10, 10), text, fill='white', font=font)
        
        image_array = np.array(img).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array)[None,]
        
        return image_tensor
