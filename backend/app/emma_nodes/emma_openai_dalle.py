"""
Emma OpenAI DALL-E Node - Usa la API de OpenAI existente de Emma
"""

import requests
import logging
import io
from typing import Tuple

class EmmaOpenAIDalle:
    """Nodo que usa la API de OpenAI DALL-E de Emma Studio"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "prompt": ("STRING", {
                    "tooltip": "Prompt para generar la imagen con DALL-E"
                }),
                "model": (["dall-e-3", "dall-e-2"], {
                    "default": "dall-e-3",
                    "tooltip": "Modelo de DALL-E a usar"
                }),
                "size": (["1024x1024", "1792x1024", "1024x1792"], {
                    "default": "1024x1024",
                    "tooltip": "Tamaño de la imagen"
                }),
                "quality": (["standard", "hd"], {
                    "default": "standard",
                    "tooltip": "Calidad de la imagen"
                }),
                "style": (["vivid", "natural"], {
                    "default": "vivid",
                    "tooltip": "Estilo de la imagen"
                }),
            }
        }
    
    RETURN_TYPES = ("IMAGE", "STRING", "STRING")
    RETURN_NAMES = ("image", "image_url", "revised_prompt")
    FUNCTION = "generate_image"
    CATEGORY = "Emma Studio/Image Generation"
    DESCRIPTION = "Genera imágenes usando OpenAI DALL-E de Emma Studio"
    
    def generate_image(self, prompt, model="dall-e-3", size="1024x1024", 
                      quality="standard", style="vivid"):
        """Genera imagen usando la API de OpenAI DALL-E existente"""
        try:
            # Preparar datos para la API
            api_data = {
                "prompt": prompt,
                "model": model,
                "size": size,
                "quality": quality,
                "style": style,
                "n": 1
            }
            
            # Llamar a la API existente de Emma
            response = requests.post(
                "http://localhost:8001/api/v1/openai-images/generate",
                json=api_data,
                timeout=120
            )
            
            if response.status_code != 200:
                raise Exception(f"Error en API OpenAI: {response.status_code}")
            
            result = response.json()
            
            if not result.get("success"):
                raise Exception(f"Error generando imagen: {result.get('error', 'Unknown error')}")
            
            image_url = result.get("image_url")
            revised_prompt = result.get("revised_prompt", prompt)
            
            if not image_url:
                raise Exception("No se recibió URL de imagen")
            
            # Descargar imagen para ComfyUI
            image_response = requests.get(image_url, timeout=30)
            image_response.raise_for_status()
            
            # Convertir a formato que ComfyUI entiende
            from PIL import Image
            import torch
            import numpy as np
            
            image = Image.open(io.BytesIO(image_response.content))
            image = image.convert("RGB")
            
            # Convertir a tensor de ComfyUI
            image_array = np.array(image).astype(np.float32) / 255.0
            image_tensor = torch.from_numpy(image_array)[None,]
            
            logging.info(f"Imagen generada exitosamente con DALL-E: {image_url}")
            
            return (image_tensor, image_url, revised_prompt)
            
        except Exception as e:
            logging.error(f"Error en EmmaOpenAIDalle: {str(e)}")
            # Retornar imagen de error
            error_image = self._create_error_image(str(e))
            return (error_image, "", prompt)
    
    def _create_error_image(self, error_message):
        """Crea una imagen de error"""
        from PIL import Image, ImageDraw, ImageFont
        import torch
        import numpy as np
        
        img = Image.new('RGB', (1024, 1024), color='blue')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"DALL-E Error: {error_message[:50]}..."
        draw.text((10, 10), text, fill='white', font=font)
        
        image_array = np.array(img).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array)[None,]
        
        return image_tensor
