"""
Emma Image Output Node - Guarda y muestra imágenes en Emma Studio
"""

import os
import logging
import tempfile
from datetime import datetime
from typing import Tuple

class EmmaImageOutput:
    """Nodo de salida para guardar y mostrar imágenes en Emma Studio"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "image": ("IMAGE", {
                    "tooltip": "Imagen a guardar y mostrar"
                }),
                "filename_prefix": ("STRING", {
                    "default": "emma_workflow",
                    "tooltip": "Prefijo para el nombre del archivo"
                }),
            },
            "optional": {
                "quality": ("INT", {
                    "default": 95,
                    "min": 1,
                    "max": 100,
                    "tooltip": "Calidad de la imagen (1-100)"
                }),
                "format": (["PNG", "JPEG", "WEBP"], {
                    "default": "PNG",
                    "tooltip": "Formato de la imagen"
                }),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("image_path", "image_url")
    FUNCTION = "save_image"
    CATEGORY = "Emma Studio/Output"
    DESCRIPTION = "Guarda y muestra imágenes generadas en workflows de Emma Studio"
    OUTPUT_NODE = True
    
    def save_image(self, image, filename_prefix="emma_workflow", quality=95, format="PNG"):
        """Guarda imagen y retorna path/URL"""
        try:
            # Convertir tensor a PIL
            import torch
            import numpy as np
            from PIL import Image
            
            if len(image.shape) == 4:
                image_array = image[0].cpu().numpy()
            else:
                image_array = image.cpu().numpy()
            
            image_array = (image_array * 255).astype(np.uint8)
            pil_image = Image.fromarray(image_array)
            
            # Generar nombre único
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{timestamp}.{format.lower()}"
            
            # Crear directorio de salida si no existe
            output_dir = os.path.join(tempfile.gettempdir(), "emma_workflows")
            os.makedirs(output_dir, exist_ok=True)
            
            # Guardar imagen
            image_path = os.path.join(output_dir, filename)
            
            if format == "JPEG":
                # Convertir a RGB para JPEG
                if pil_image.mode in ("RGBA", "LA", "P"):
                    pil_image = pil_image.convert("RGB")
                pil_image.save(image_path, format=format, quality=quality, optimize=True)
            elif format == "WEBP":
                pil_image.save(image_path, format=format, quality=quality, optimize=True)
            else:  # PNG
                pil_image.save(image_path, format=format, optimize=True)
            
            # Generar URL temporal para servir la imagen
            image_url = f"http://localhost:8001/temp/{filename}"
            
            logging.info(f"Imagen guardada exitosamente: {image_path}")
            
            # Retornar información para ComfyUI UI
            return {
                "ui": {
                    "images": [{
                        "filename": filename,
                        "subfolder": "",
                        "type": "temp"
                    }]
                },
                "result": (image_path, image_url)
            }
            
        except Exception as e:
            logging.error(f"Error en EmmaImageOutput: {str(e)}")
            return {
                "ui": {"images": []},
                "result": ("", "")
            }
