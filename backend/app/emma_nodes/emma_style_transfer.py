"""
Emma Style Transfer Node - Usa la API de Style Transfer existente de Emma
"""

import requests
import logging
import base64
import io
from typing import Tuple

class EmmaStyleTransfer:
    """Nodo que usa la API de Style Transfer de Emma Studio"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "content_image": ("IMAGE", {
                    "tooltip": "Imagen de contenido"
                }),
                "style_image": ("IMAGE", {
                    "tooltip": "Imagen de estilo de referencia"
                }),
                "strength": ("FLOAT", {
                    "default": 0.7,
                    "min": 0.1,
                    "max": 1.0,
                    "step": 0.1,
                    "tooltip": "Intensidad de la transferencia de estilo"
                }),
            },
            "optional": {
                "prompt": ("STRING", {
                    "default": "",
                    "tooltip": "Prompt adicional para guiar el estilo (opcional)"
                }),
            }
        }
    
    RETURN_TYPES = ("IMAGE", "STRING")
    RETURN_NAMES = ("styled_image", "image_url")
    FUNCTION = "transfer_style"
    CATEGORY = "Emma Studio/Image Editing"
    DESCRIPTION = "Transfiere estilo entre imágenes usando las APIs de Emma Studio"
    
    def transfer_style(self, content_image, style_image, strength=0.7, prompt=""):
        """Transfiere estilo usando la API existente"""
        try:
            # Convertir imágenes a base64
            content_b64 = self._image_to_base64(content_image)
            style_b64 = self._image_to_base64(style_image)
            
            # Preparar datos para la API
            api_data = {
                "content_image": content_b64,
                "style_image": style_b64,
                "strength": strength
            }
            
            if prompt:
                api_data["prompt"] = prompt
            
            # Llamar a la API existente de Emma
            response = requests.post(
                "http://localhost:8001/api/v1/images/style-transfer",
                json=api_data,
                timeout=120
            )
            
            if response.status_code != 200:
                raise Exception(f"Error en API Style Transfer: {response.status_code}")
            
            result = response.json()
            
            if not result.get("success"):
                raise Exception(f"Error en transferencia de estilo: {result.get('error', 'Unknown error')}")
            
            # Obtener imagen con estilo transferido
            styled_url = result.get("styled_url")
            if not styled_url:
                raise Exception("No se recibió URL de imagen con estilo")
            
            # Descargar imagen estilizada
            image_response = requests.get(styled_url, timeout=30)
            image_response.raise_for_status()
            
            # Convertir a tensor de ComfyUI
            styled_image = self._response_to_tensor(image_response.content)
            
            logging.info(f"Estilo transferido exitosamente: {styled_url}")
            
            return (styled_image, styled_url)
            
        except Exception as e:
            logging.error(f"Error en EmmaStyleTransfer: {str(e)}")
            # Retornar imagen de contenido original en caso de error
            return (content_image, "")
    
    def _image_to_base64(self, image_tensor):
        """Convierte tensor de imagen a base64"""
        import torch
        import numpy as np
        from PIL import Image
        
        if len(image_tensor.shape) == 4:
            image_array = image_tensor[0].cpu().numpy()
        else:
            image_array = image_tensor.cpu().numpy()
        
        image_array = (image_array * 255).astype(np.uint8)
        pil_image = Image.fromarray(image_array)
        
        buffer = io.BytesIO()
        pil_image.save(buffer, format='PNG')
        return base64.b64encode(buffer.getvalue()).decode()
    
    def _response_to_tensor(self, image_bytes):
        """Convierte bytes de imagen a tensor de ComfyUI"""
        from PIL import Image
        import torch
        import numpy as np
        
        image = Image.open(io.BytesIO(image_bytes))
        image = image.convert("RGB")
        
        image_array = np.array(image).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array)[None,]
        
        return image_tensor
