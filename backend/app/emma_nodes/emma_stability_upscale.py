"""
Emma Stability Upscale Node - Usa la API de Stability AI existente de Emma
"""

import requests
import logging
import base64
import io
from typing import Tuple

class EmmaStabilityUpscale:
    """Nodo que usa la API de Stability AI de Emma Studio para upscaling"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "image": ("IMAGE", {
                    "tooltip": "Imagen a mejorar"
                }),
                "scale_factor": ([2, 4], {
                    "default": 2,
                    "tooltip": "Factor de escalado (2x o 4x)"
                }),
                "creativity": ("FLOAT", {
                    "default": 0.3,
                    "min": 0.0,
                    "max": 1.0,
                    "step": 0.1,
                    "tooltip": "Nivel de creatividad (0.0 = conservador, 1.0 = creativo)"
                }),
            },
            "optional": {
                "prompt": ("STRING", {
                    "default": "",
                    "tooltip": "Prompt para guiar el upscaling (opcional)"
                }),
            }
        }
    
    RETURN_TYPES = ("IMAGE", "STRING")
    RETURN_NAMES = ("upscaled_image", "image_url")
    FUNCTION = "upscale_image"
    CATEGORY = "Emma Studio/Image Enhancement"
    DESCRIPTION = "Mejora la resolución de imágenes usando Stability AI de Emma Studio"
    
    def upscale_image(self, image, scale_factor=2, creativity=0.3, prompt=""):
        """Mejora imagen usando la API de Stability AI existente"""
        try:
            # Convertir tensor de ComfyUI a imagen PIL
            import torch
            import numpy as np
            from PIL import Image
            
            # Convertir de tensor [batch, height, width, channels] a PIL
            if len(image.shape) == 4:
                image_array = image[0].cpu().numpy()
            else:
                image_array = image.cpu().numpy()
            
            # Convertir de [0,1] a [0,255]
            image_array = (image_array * 255).astype(np.uint8)
            pil_image = Image.fromarray(image_array)
            
            # Convertir a base64 para la API
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Preparar datos para la API
            api_data = {
                "image": image_base64,
                "scale_factor": scale_factor,
                "creativity": creativity
            }
            
            if prompt:
                api_data["prompt"] = prompt
            
            # Llamar a la API existente de Emma
            response = requests.post(
                "http://localhost:8001/api/v1/ai-editor/upscale",
                json=api_data,
                timeout=120
            )
            
            if response.status_code != 200:
                raise Exception(f"Error en API Stability: {response.status_code}")
            
            result = response.json()
            
            if not result.get("success"):
                raise Exception(f"Error en upscaling: {result.get('error', 'Unknown error')}")
            
            # Obtener imagen mejorada
            upscaled_url = result.get("upscaled_url")
            if not upscaled_url:
                raise Exception("No se recibió URL de imagen mejorada")
            
            # Descargar imagen mejorada
            image_response = requests.get(upscaled_url, timeout=30)
            image_response.raise_for_status()
            
            # Convertir a tensor de ComfyUI
            upscaled_image = Image.open(io.BytesIO(image_response.content))
            upscaled_image = upscaled_image.convert("RGB")
            
            image_array = np.array(upscaled_image).astype(np.float32) / 255.0
            image_tensor = torch.from_numpy(image_array)[None,]
            
            logging.info(f"Imagen mejorada exitosamente con Stability AI: {upscaled_url}")
            
            return (image_tensor, upscaled_url)
            
        except Exception as e:
            logging.error(f"Error en EmmaStabilityUpscale: {str(e)}")
            # Retornar imagen original en caso de error
            return (image, "")
    
    def _create_error_image(self, error_message):
        """Crea una imagen de error"""
        from PIL import Image, ImageDraw, ImageFont
        import torch
        import numpy as np
        
        img = Image.new('RGB', (512, 512), color='orange')
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"Upscale Error: {error_message[:40]}..."
        draw.text((10, 10), text, fill='white', font=font)
        
        image_array = np.array(img).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array)[None,]
        
        return image_tensor
