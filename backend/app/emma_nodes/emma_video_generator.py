"""
Emma Video Generator Node - Usa las APIs de video existentes de Emma
"""

import requests
import logging
import base64
import io
from typing import Tuple

class EmmaVideoGenerator:
    """Nodo que usa las APIs de generación de video de Emma Studio"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "prompt": ("STRING", {
                    "tooltip": "Prompt para generar el video"
                }),
                "provider": (["luma-labs", "runway-ml", "stability-video"], {
                    "default": "luma-labs",
                    "tooltip": "Proveedor de generación de video"
                }),
                "duration": (["5", "10", "15"], {
                    "default": "5",
                    "tooltip": "Duración del video en segundos"
                }),
            },
            "optional": {
                "reference_image": ("IMAGE", {
                    "tooltip": "Imagen de referencia para el video (opcional)"
                }),
                "aspect_ratio": (["16:9", "9:16", "1:1"], {
                    "default": "16:9",
                    "tooltip": "Relación de aspecto del video"
                }),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("video_url", "generation_id")
    FUNCTION = "generate_video"
    CATEGORY = "Emma Studio/Video Generation"
    DESCRIPTION = "Genera videos usando las APIs de video de Emma Studio"
    
    def generate_video(self, prompt, provider="luma-labs", duration="5", 
                      reference_image=None, aspect_ratio="16:9"):
        """Genera video usando las APIs existentes"""
        try:
            # Preparar datos base
            api_data = {
                "prompt": prompt,
                "duration": int(duration),
                "aspect_ratio": aspect_ratio
            }
            
            # Agregar imagen de referencia si existe
            if reference_image is not None:
                reference_b64 = self._image_to_base64(reference_image)
                api_data["reference_image"] = reference_b64
            
            # Seleccionar endpoint según proveedor
            if provider == "luma-labs":
                endpoint = "http://localhost:8001/api/luma-labs/generate"
            elif provider == "runway-ml":
                endpoint = "http://localhost:8001/api/v1/videos/runway-generate"
            elif provider == "stability-video":
                endpoint = "http://localhost:8001/api/v1/videos/stability-generate"
            else:
                raise Exception(f"Proveedor no soportado: {provider}")
            
            # Llamar a la API
            response = requests.post(
                endpoint,
                json=api_data,
                timeout=300  # 5 minutos para videos
            )
            
            if response.status_code != 200:
                raise Exception(f"Error en API {provider}: {response.status_code}")
            
            result = response.json()
            
            if not result.get("success"):
                raise Exception(f"Error generando video: {result.get('error', 'Unknown error')}")
            
            video_url = result.get("video_url")
            generation_id = result.get("generation_id", "")
            
            if not video_url:
                raise Exception("No se recibió URL de video")
            
            logging.info(f"Video generado exitosamente con {provider}: {video_url}")
            
            return (video_url, generation_id)
            
        except Exception as e:
            logging.error(f"Error en EmmaVideoGenerator: {str(e)}")
            return ("", "")
    
    def _image_to_base64(self, image_tensor):
        """Convierte tensor de imagen a base64"""
        import torch
        import numpy as np
        from PIL import Image
        
        if len(image_tensor.shape) == 4:
            image_array = image_tensor[0].cpu().numpy()
        else:
            image_array = image_tensor.cpu().numpy()
        
        image_array = (image_array * 255).astype(np.uint8)
        pil_image = Image.fromarray(image_array)
        
        buffer = io.BytesIO()
        pil_image.save(buffer, format='PNG')
        return base64.b64encode(buffer.getvalue()).decode()
