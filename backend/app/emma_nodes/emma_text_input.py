"""
Emma Text Input Node - Nodo de entrada de texto para workflows
"""

class EmmaTextInput:
    """Nodo de entrada de texto para workflows Emma"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "text": ("STRING", {
                    "multiline": True,
                    "default": "beautiful landscape, masterpiece, best quality",
                    "tooltip": "Texto de entrada para el workflow"
                }),
            },
            "optional": {
                "negative_prompt": ("STRING", {
                    "multiline": True,
                    "default": "bad quality, blurry, low resolution",
                    "tooltip": "Prompt negativo (opcional)"
                }),
            }
        }
    
    RETURN_TYPES = ("STRING", "STRING")
    RETURN_NAMES = ("positive_prompt", "negative_prompt")
    FUNCTION = "process_text"
    CATEGORY = "Emma Studio/Input"
    DESCRIPTION = "Nodo de entrada de texto para workflows de Emma Studio"
    
    def process_text(self, text, negative_prompt=""):
        """Procesa el texto de entrada"""
        return (text.strip(), negative_prompt.strip())
