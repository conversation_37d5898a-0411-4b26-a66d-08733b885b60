"""
Emma Studio Custom Nodes - Nodos personalizados que usan las APIs existentes de Emma
"""

from .emma_text_input import EmmaTextInput
from .emma_ideogram_generator import EmmaIdeogramGenerator
from .emma_stability_upscale import EmmaStabilityUpscale
from .emma_openai_dalle import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .emma_background_remover import Emma<PERSON><PERSON>groundRemover
from .emma_style_transfer import Emma<PERSON>tyleTransfer
from .emma_video_generator import EmmaVideoGenerator
from .emma_image_output import EmmaImageOutput

# Mapeo de nodos Emma para ComfyUI
EMMA_NODE_CLASS_MAPPINGS = {
    "EmmaTextInput": EmmaTextInput,
    "EmmaIdeogramGenerator": EmmaIdeogramGenerator,
    "EmmaStabilityUpscale": EmmaStabilityUpscale,
    "EmmaOpenAIDalle": EmmaOpenAIDalle,
    "EmmaBackgroundRemover": EmmaBackgroundRemover,
    "EmmaStyleTransfer": EmmaStyleTransfer,
    "EmmaVideoGenerator": <PERSON>V<PERSON>oGenerator,
    "EmmaImageOutput": EmmaImageOutput,
}

# Nombres de display para la UI
EMMA_NODE_DISPLAY_NAME_MAPPINGS = {
    "EmmaTextInput": "📝 Emma Text Input",
    "EmmaIdeogramGenerator": "🎨 Emma Ideogram Generator",
    "EmmaStabilityUpscale": "⬆️ Emma Stability Upscale",
    "EmmaOpenAIDalle": "🤖 Emma DALL-E Generator",
    "EmmaBackgroundRemover": "🗑️ Emma Background Remover",
    "EmmaStyleTransfer": "🎭 Emma Style Transfer",
    "EmmaVideoGenerator": "🎬 Emma Video Generator",
    "EmmaImageOutput": "💾 Emma Image Output",
}

__all__ = [
    "EMMA_NODE_CLASS_MAPPINGS",
    "EMMA_NODE_DISPLAY_NAME_MAPPINGS",
    "EmmaTextInput",
    "EmmaIdeogramGenerator",
    "EmmaStabilityUpscale",
    "EmmaOpenAIDalle",
    "EmmaBackgroundRemover",
    "EmmaStyleTransfer",
    "EmmaVideoGenerator",
    "EmmaImageOutput",
]
