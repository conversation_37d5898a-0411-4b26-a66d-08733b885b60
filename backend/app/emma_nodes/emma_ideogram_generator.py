"""
Emma Ideogram Generator Node - Usa la API de Ideogram existente de Emma
"""

import requests
import asyncio
import logging
from typing import Tuple

class EmmaIdeogramGenerator:
    """Nodo que usa la API de Ideogram de Emma Studio"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "prompt": ("STRING", {
                    "tooltip": "Prompt para generar la imagen"
                }),
                "model": (["ideogram-3.0", "ideogram-2.0"], {
                    "default": "ideogram-3.0",
                    "tooltip": "Modelo de Ideogram a usar"
                }),
                "aspect_ratio": (["1:1", "16:9", "9:16", "4:3", "3:4"], {
                    "default": "1:1",
                    "tooltip": "Relación de aspecto de la imagen"
                }),
                "style": (["auto", "realistic", "design", "anime", "3d"], {
                    "default": "auto",
                    "tooltip": "Estilo de la imagen"
                }),
            },
            "optional": {
                "negative_prompt": ("STRING", {
                    "default": "",
                    "tooltip": "Prompt negativo (opcional)"
                }),
                "seed": ("INT", {
                    "default": -1,
                    "min": -1,
                    "max": 2147483647,
                    "tooltip": "Semilla para reproducibilidad (-1 para aleatoria)"
                }),
            }
        }
    
    RETURN_TYPES = ("IMAGE", "STRING")
    RETURN_NAMES = ("image", "image_url")
    FUNCTION = "generate_image"
    CATEGORY = "Emma Studio/Image Generation"
    DESCRIPTION = "Genera imágenes usando la API de Ideogram de Emma Studio"
    
    def generate_image(self, prompt, model="ideogram-3.0", aspect_ratio="1:1", 
                      style="auto", negative_prompt="", seed=-1):
        """Genera imagen usando la API de Ideogram existente"""
        try:
            # Preparar datos para la API
            api_data = {
                "prompt": prompt,
                "model": model,
                "aspect_ratio": aspect_ratio,
                "style": style,
                "magic_prompt_option": "AUTO"
            }
            
            if negative_prompt:
                api_data["negative_prompt"] = negative_prompt
            
            if seed != -1:
                api_data["seed"] = seed
            
            # Llamar a la API existente de Emma
            response = requests.post(
                "http://localhost:8001/api/image-generator/generate",
                json=api_data,
                timeout=120
            )
            
            if response.status_code != 200:
                raise Exception(f"Error en API Ideogram: {response.status_code}")
            
            result = response.json()
            
            if not result.get("success"):
                raise Exception(f"Error generando imagen: {result.get('error', 'Unknown error')}")
            
            image_url = result.get("image_url")
            if not image_url:
                raise Exception("No se recibió URL de imagen")
            
            # Descargar imagen para ComfyUI
            image_response = requests.get(image_url, timeout=30)
            image_response.raise_for_status()
            
            # Convertir a formato que ComfyUI entiende
            from PIL import Image
            import io
            import torch
            import numpy as np
            
            image = Image.open(io.BytesIO(image_response.content))
            image = image.convert("RGB")
            
            # Convertir a tensor de ComfyUI (formato: [batch, height, width, channels])
            image_array = np.array(image).astype(np.float32) / 255.0
            image_tensor = torch.from_numpy(image_array)[None,]
            
            logging.info(f"Imagen generada exitosamente con Ideogram: {image_url}")
            
            return (image_tensor, image_url)
            
        except Exception as e:
            logging.error(f"Error en EmmaIdeogramGenerator: {str(e)}")
            # Retornar imagen de error
            error_image = self._create_error_image(str(e))
            return (error_image, "")
    
    def _create_error_image(self, error_message):
        """Crea una imagen de error"""
        from PIL import Image, ImageDraw, ImageFont
        import torch
        import numpy as np
        
        # Crear imagen de error
        img = Image.new('RGB', (512, 512), color='red')
        draw = ImageDraw.Draw(img)
        
        try:
            # Intentar usar fuente del sistema
            font = ImageFont.load_default()
        except:
            font = None
        
        # Escribir mensaje de error
        text = f"Error: {error_message[:50]}..."
        draw.text((10, 10), text, fill='white', font=font)
        
        # Convertir a tensor
        image_array = np.array(img).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array)[None,]
        
        return image_tensor
