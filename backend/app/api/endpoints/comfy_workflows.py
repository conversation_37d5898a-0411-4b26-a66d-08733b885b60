"""
ComfyUI Workflows API - Proxy directo a ComfyUI para Emma Studio
Usa ComfyUI completo sin modificaciones
"""

import os
import sys
import json
import asyncio
import tempfile
import logging
import subprocess
import requests
import time
from typing import Dict, Any, List, Optional
from pathlib import Path

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

# Configuración ComfyUI
COMFY_PATH = Path(__file__).parent.parent.parent.parent.parent / "ComfyUI"
COMFY_URL = "http://127.0.0.1:8188"
COMFY_PROCESS = None

# Agregar ComfyUI al path para importar módulos
sys.path.insert(0, str(COMFY_PATH))

# Verificar si ComfyUI está disponible
COMFY_AVAILABLE = COMFY_PATH.exists() and (COMFY_PATH / "main.py").exists()

# Importar nodos Emma
try:
    from app.emma_nodes import EMMA_NODE_CLASS_MAPPINGS, EMMA_NODE_DISPLAY_NAME_MAPPINGS
    EMMA_NODES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Emma nodes no disponibles: {e}")
    EMMA_NODES_AVAILABLE = False
    EMMA_NODE_CLASS_MAPPINGS = {}
    EMMA_NODE_DISPLAY_NAME_MAPPINGS = {}

router = APIRouter()

# Modelos Pydantic
class WorkflowNode(BaseModel):
    """Representa un nodo en el workflow"""
    id: str
    class_type: str
    inputs: Dict[str, Any] = {}
    position: Dict[str, float] = {"x": 0, "y": 0}

class WorkflowConnection(BaseModel):
    """Representa una conexión entre nodos"""
    source_node: str
    source_output: int
    target_node: str
    target_input: str

class VisualWorkflow(BaseModel):
    """Workflow visual completo"""
    nodes: List[WorkflowNode]
    connections: List[WorkflowConnection]
    metadata: Dict[str, Any] = {}

class ComfyWorkflow(BaseModel):
    """Workflow en formato ComfyUI"""
    workflow: Dict[str, Any]
    extra_data: Dict[str, Any] = {}

class WorkflowExecutionRequest(BaseModel):
    """Request para ejecutar workflow"""
    workflow: Dict[str, Any]
    client_id: Optional[str] = None

class WorkflowExecutionResponse(BaseModel):
    """Response de ejecución de workflow"""
    success: bool
    execution_id: str
    message: str
    outputs: Dict[str, Any] = {}
    errors: List[str] = []

# Funciones para manejar ComfyUI
def setup_emma_nodes():
    """Configura los nodos Emma en ComfyUI"""
    if not EMMA_NODES_AVAILABLE:
        return False

    try:
        # Crear archivo de nodos Emma para ComfyUI
        emma_nodes_file = COMFY_PATH / "custom_nodes" / "emma_nodes.py"

        # Crear directorio custom_nodes si no existe
        (COMFY_PATH / "custom_nodes").mkdir(exist_ok=True)

        # Escribir archivo de nodos Emma
        with open(emma_nodes_file, 'w') as f:
            f.write(f"""
# Emma Studio Custom Nodes for ComfyUI
import sys
import os

# Agregar path de Emma Studio
emma_path = r"{Path(__file__).parent.parent.parent.parent.parent}"
sys.path.insert(0, emma_path)

try:
    from backend.app.emma_nodes import EMMA_NODE_CLASS_MAPPINGS, EMMA_NODE_DISPLAY_NAME_MAPPINGS

    # Registrar nodos Emma
    NODE_CLASS_MAPPINGS = EMMA_NODE_CLASS_MAPPINGS
    NODE_DISPLAY_NAME_MAPPINGS = EMMA_NODE_DISPLAY_NAME_MAPPINGS

    print("✅ Emma Studio nodes loaded successfully!")

except Exception as e:
    print(f"❌ Error loading Emma nodes: {{e}}")
    NODE_CLASS_MAPPINGS = {{}}
    NODE_DISPLAY_NAME_MAPPINGS = {{}}

__all__ = ["NODE_CLASS_MAPPINGS", "NODE_DISPLAY_NAME_MAPPINGS"]
""")

        logging.info("Nodos Emma configurados exitosamente")
        return True

    except Exception as e:
        logging.error(f"Error configurando nodos Emma: {e}")
        return False

def start_comfyui_server():
    """Inicia el servidor ComfyUI si no está corriendo"""
    global COMFY_PROCESS

    if not COMFY_AVAILABLE:
        logging.error("ComfyUI no está disponible en el path especificado")
        return False

    # Verificar si ya está corriendo
    if is_comfyui_running():
        logging.info("ComfyUI ya está corriendo")
        return True

    try:
        # Configurar nodos Emma antes de iniciar
        setup_emma_nodes()

        # Iniciar ComfyUI
        logging.info("Iniciando servidor ComfyUI con nodos Emma...")
        COMFY_PROCESS = subprocess.Popen(
            [sys.executable, "main.py", "--listen", "127.0.0.1", "--port", "8188"],
            cwd=str(COMFY_PATH),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Esperar a que el servidor esté listo
        for _ in range(30):  # 30 segundos máximo
            if is_comfyui_running():
                logging.info("ComfyUI iniciado exitosamente con nodos Emma")
                return True
            time.sleep(1)

        logging.error("ComfyUI no pudo iniciarse en el tiempo esperado")
        return False

    except Exception as e:
        logging.error(f"Error iniciando ComfyUI: {e}")
        return False

def is_comfyui_running() -> bool:
    """Verifica si ComfyUI está corriendo"""
    try:
        response = requests.get(f"{COMFY_URL}/system_stats", timeout=2)
        return response.status_code == 200
    except:
        return False

def stop_comfyui_server():
    """Detiene el servidor ComfyUI"""
    global COMFY_PROCESS
    if COMFY_PROCESS:
        COMFY_PROCESS.terminate()
        COMFY_PROCESS = None

async def proxy_to_comfyui(endpoint: str, method: str = "GET", data: Dict = None) -> Dict:
    """Proxy requests a ComfyUI"""
    if not is_comfyui_running():
        if not start_comfyui_server():
            raise HTTPException(status_code=503, detail="ComfyUI no está disponible")

    url = f"{COMFY_URL}{endpoint}"

    try:
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=30)
        else:
            raise ValueError(f"Método HTTP no soportado: {method}")

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        logging.error(f"Error en proxy a ComfyUI: {e}")
        raise HTTPException(status_code=500, detail=f"Error comunicándose con ComfyUI: {str(e)}")

# Adaptador ComfyUI
class ComfyUIAdapter:
    """Adaptador que usa ComfyUI completo via API"""

    def __init__(self):
        self.available = COMFY_AVAILABLE

    async def get_available_nodes(self) -> Dict[str, Any]:
        """Obtiene todos los nodos disponibles via API de ComfyUI"""
        try:
            return await proxy_to_comfyui("/object_info")
        except Exception as e:
            logging.error(f"Error obteniendo nodos: {e}")
            return {}

    async def queue_prompt(self, workflow: Dict[str, Any], client_id: str = None) -> Dict[str, Any]:
        """Envía un workflow a la cola de ComfyUI"""
        prompt_data = {
            "prompt": workflow,
            "client_id": client_id or f"emma_{int(time.time())}"
        }

        return await proxy_to_comfyui("/prompt", "POST", prompt_data)

    async def get_queue_status(self) -> Dict[str, Any]:
        """Obtiene el estado de la cola"""
        return await proxy_to_comfyui("/queue")

    async def get_history(self, prompt_id: str = None) -> Dict[str, Any]:
        """Obtiene el historial de ejecuciones"""
        endpoint = f"/history/{prompt_id}" if prompt_id else "/history"
        return await proxy_to_comfyui(endpoint)

    async def interrupt_execution(self) -> Dict[str, Any]:
        """Interrumpe la ejecución actual"""
        return await proxy_to_comfyui("/interrupt", "POST", {})

# Instancia global del adaptador
comfy_adapter = ComfyUIAdapter()

@router.get("/nodes")
async def get_available_nodes():
    """Obtiene todos los nodos disponibles de ComfyUI"""
    try:
        nodes_info = await comfy_adapter.get_available_nodes()
        return {
            "success": True,
            "nodes": nodes_info,
            "total": len(nodes_info)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo nodos: {str(e)}")

@router.get("/nodes/categories")
async def get_node_categories():
    """Obtiene las categorías de nodos disponibles"""
    try:
        nodes_info = await comfy_adapter.get_available_nodes()
        categories = {}

        for node_name, node_info in nodes_info.items():
            category = node_info.get("category", "uncategorized")
            if category not in categories:
                categories[category] = []
            categories[category].append({
                "name": node_name,
                "display_name": node_info.get("display_name", node_name),
                "description": node_info.get("description", "")
            })

        return {
            "success": True,
            "categories": categories
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo categorías: {str(e)}")

@router.get("/nodes/emma")
async def get_emma_nodes():
    """Obtiene solo los nodos Emma personalizados"""
    try:
        if not EMMA_NODES_AVAILABLE:
            return {
                "success": False,
                "message": "Nodos Emma no disponibles",
                "nodes": {}
            }

        emma_nodes = {}
        categories = {}

        for node_name, node_class in EMMA_NODE_CLASS_MAPPINGS.items():
            try:
                input_types = node_class.INPUT_TYPES()
                return_types = getattr(node_class, 'RETURN_TYPES', ())
                category = getattr(node_class, 'CATEGORY', 'Emma Studio')
                description = getattr(node_class, 'DESCRIPTION', '')

                emma_nodes[node_name] = {
                    "display_name": EMMA_NODE_DISPLAY_NAME_MAPPINGS.get(node_name, node_name),
                    "category": category,
                    "description": description,
                    "input_types": input_types,
                    "return_types": return_types,
                    "function": getattr(node_class, 'FUNCTION', 'execute')
                }

                # Organizar por categorías
                if category not in categories:
                    categories[category] = []
                categories[category].append({
                    "name": node_name,
                    "display_name": EMMA_NODE_DISPLAY_NAME_MAPPINGS.get(node_name, node_name),
                    "description": description
                })

            except Exception as e:
                logging.warning(f"Error procesando nodo Emma {node_name}: {e}")
                continue

        return {
            "success": True,
            "nodes": emma_nodes,
            "categories": categories,
            "total": len(emma_nodes)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo nodos Emma: {str(e)}")

@router.post("/execute")
async def execute_workflow(request: WorkflowExecutionRequest):
    """Ejecuta un workflow ComfyUI directamente"""
    try:
        if not comfy_adapter.available:
            raise HTTPException(status_code=503, detail="ComfyUI no está disponible")

        # Enviar workflow a ComfyUI
        result = await comfy_adapter.queue_prompt(request.workflow, request.client_id)

        return WorkflowExecutionResponse(
            success=True,
            execution_id=result.get("prompt_id", "unknown"),
            message="Workflow enviado a ComfyUI exitosamente",
            outputs=result
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ejecutando workflow: {str(e)}")

@router.get("/queue")
async def get_queue_status():
    """Obtiene el estado de la cola de ComfyUI"""
    try:
        queue_status = await comfy_adapter.get_queue_status()
        return {
            "success": True,
            "queue": queue_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo estado de cola: {str(e)}")

@router.get("/history/{prompt_id}")
async def get_execution_history(prompt_id: str):
    """Obtiene el historial de una ejecución específica"""
    try:
        history = await comfy_adapter.get_history(prompt_id)
        return {
            "success": True,
            "history": history
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo historial: {str(e)}")

@router.post("/interrupt")
async def interrupt_execution():
    """Interrumpe la ejecución actual"""
    try:
        result = await comfy_adapter.interrupt_execution()
        return {
            "success": True,
            "message": "Ejecución interrumpida",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error interrumpiendo ejecución: {str(e)}")

@router.get("/templates")
async def get_workflow_templates():
    """Obtiene plantillas de workflows predefinidas"""
    templates = {
        "emma_ideogram_basic": {
            "name": "🎨 Emma Ideogram Básico",
            "description": "Genera imagen con Ideogram y mejora con Stability AI",
            "workflow": {
                "1": {
                    "class_type": "EmmaTextInput",
                    "inputs": {
                        "text": "beautiful landscape, masterpiece, best quality",
                        "negative_prompt": "bad quality, blurry, low resolution"
                    }
                },
                "2": {
                    "class_type": "EmmaIdeogramGenerator",
                    "inputs": {
                        "prompt": ["1", 0],
                        "model": "ideogram-3.0",
                        "aspect_ratio": "1:1",
                        "style": "realistic"
                    }
                },
                "3": {
                    "class_type": "EmmaStabilityUpscale",
                    "inputs": {
                        "image": ["2", 0],
                        "scale_factor": 2,
                        "creativity": 0.3
                    }
                },
                "4": {
                    "class_type": "EmmaImageOutput",
                    "inputs": {
                        "image": ["3", 0],
                        "filename_prefix": "emma_ideogram",
                        "format": "PNG"
                    }
                }
            }
        },
        "emma_dalle_workflow": {
            "name": "🤖 Emma DALL-E + Edición",
            "description": "Genera con DALL-E y remueve fondo",
            "workflow": {
                "1": {
                    "class_type": "EmmaTextInput",
                    "inputs": {
                        "text": "a cute robot in a futuristic city"
                    }
                },
                "2": {
                    "class_type": "EmmaOpenAIDalle",
                    "inputs": {
                        "prompt": ["1", 0],
                        "model": "dall-e-3",
                        "size": "1024x1024",
                        "quality": "hd",
                        "style": "vivid"
                    }
                },
                "3": {
                    "class_type": "EmmaBackgroundRemover",
                    "inputs": {
                        "image": ["2", 0]
                    }
                },
                "4": {
                    "class_type": "EmmaImageOutput",
                    "inputs": {
                        "image": ["3", 0],
                        "filename_prefix": "emma_dalle_nobg",
                        "format": "PNG"
                    }
                }
            }
        },
        "emma_style_transfer": {
            "name": "🎭 Emma Style Transfer",
            "description": "Transfiere estilo entre dos imágenes",
            "workflow": {
                "1": {
                    "class_type": "EmmaTextInput",
                    "inputs": {
                        "text": "content image prompt"
                    }
                },
                "2": {
                    "class_type": "EmmaTextInput",
                    "inputs": {
                        "text": "style image prompt"
                    }
                },
                "3": {
                    "class_type": "EmmaIdeogramGenerator",
                    "inputs": {
                        "prompt": ["1", 0],
                        "model": "ideogram-3.0",
                        "style": "realistic"
                    }
                },
                "4": {
                    "class_type": "EmmaIdeogramGenerator",
                    "inputs": {
                        "prompt": ["2", 0],
                        "model": "ideogram-3.0",
                        "style": "design"
                    }
                },
                "5": {
                    "class_type": "EmmaStyleTransfer",
                    "inputs": {
                        "content_image": ["3", 0],
                        "style_image": ["4", 0],
                        "strength": 0.7
                    }
                },
                "6": {
                    "class_type": "EmmaImageOutput",
                    "inputs": {
                        "image": ["5", 0],
                        "filename_prefix": "emma_style_transfer",
                        "format": "PNG"
                    }
                }
            }
        },
        "emma_video_generation": {
            "name": "🎬 Emma Video Generation",
            "description": "Genera imagen y luego video",
            "workflow": {
                "1": {
                    "class_type": "EmmaTextInput",
                    "inputs": {
                        "text": "a serene lake with mountains in the background"
                    }
                },
                "2": {
                    "class_type": "EmmaIdeogramGenerator",
                    "inputs": {
                        "prompt": ["1", 0],
                        "model": "ideogram-3.0",
                        "aspect_ratio": "16:9",
                        "style": "realistic"
                    }
                },
                "3": {
                    "class_type": "EmmaVideoGenerator",
                    "inputs": {
                        "prompt": "camera slowly panning across the lake",
                        "provider": "luma-labs",
                        "duration": "5",
                        "reference_image": ["2", 0],
                        "aspect_ratio": "16:9"
                    }
                }
            }
        }
    }

    return {
        "success": True,
        "templates": templates
    }

@router.get("/health")
async def health_check():
    """Verifica el estado del sistema ComfyUI"""
    comfy_running = is_comfyui_running()

    return {
        "success": True,
        "comfy_available": comfy_adapter.available,
        "comfy_running": comfy_running,
        "comfy_url": COMFY_URL,
        "message": "ComfyUI Workflows API funcionando correctamente"
    }

@router.post("/start")
async def start_comfyui():
    """Inicia el servidor ComfyUI"""
    try:
        if is_comfyui_running():
            return {
                "success": True,
                "message": "ComfyUI ya está corriendo",
                "url": COMFY_URL
            }

        success = start_comfyui_server()
        if success:
            return {
                "success": True,
                "message": "ComfyUI iniciado exitosamente",
                "url": COMFY_URL
            }
        else:
            raise HTTPException(status_code=500, detail="No se pudo iniciar ComfyUI")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error iniciando ComfyUI: {str(e)}")

@router.post("/stop")
async def stop_comfyui():
    """Detiene el servidor ComfyUI"""
    try:
        stop_comfyui_server()
        return {
            "success": True,
            "message": "ComfyUI detenido exitosamente"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deteniendo ComfyUI: {str(e)}")

# Proxy directo a ComfyUI para funcionalidades avanzadas
@router.get("/proxy/{path:path}")
async def proxy_get(path: str):
    """Proxy GET requests a ComfyUI"""
    return await proxy_to_comfyui(f"/{path}")

@router.post("/proxy/{path:path}")
async def proxy_post(path: str, data: Dict = None):
    """Proxy POST requests a ComfyUI"""
    return await proxy_to_comfyui(f"/{path}", "POST", data)
