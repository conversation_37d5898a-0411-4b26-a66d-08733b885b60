"""
ComfyUI Workflows API - Sistema de workflows visuales de generación de imágenes AI
Adaptado de ComfyUI para Emma Studio
"""

import os
import sys
import json
import asyncio
import tempfile
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

# Agregar ComfyUI al path
COMFY_PATH = Path(__file__).parent.parent.parent.parent.parent / "ComfyUI"
sys.path.insert(0, str(COMFY_PATH))

try:
    import nodes
    import execution
    from comfy_execution.graph import DynamicPrompt
    from comfy_execution.validation import validate_node_input
    import folder_paths
    COMFY_AVAILABLE = True
except ImportError as e:
    logging.warning(f"ComfyUI no disponible: {e}")
    COMFY_AVAILABLE = False

router = APIRouter()

# Modelos Pydantic
class WorkflowNode(BaseModel):
    """Representa un nodo en el workflow"""
    id: str
    class_type: str
    inputs: Dict[str, Any] = {}
    position: Dict[str, float] = {"x": 0, "y": 0}

class WorkflowConnection(BaseModel):
    """Representa una conexión entre nodos"""
    source_node: str
    source_output: int
    target_node: str
    target_input: str

class VisualWorkflow(BaseModel):
    """Workflow visual completo"""
    nodes: List[WorkflowNode]
    connections: List[WorkflowConnection]
    metadata: Dict[str, Any] = {}

class ComfyWorkflow(BaseModel):
    """Workflow en formato ComfyUI"""
    workflow: Dict[str, Any]
    extra_data: Dict[str, Any] = {}

class WorkflowExecutionRequest(BaseModel):
    """Request para ejecutar workflow"""
    workflow: Dict[str, Any]
    client_id: Optional[str] = None

class WorkflowExecutionResponse(BaseModel):
    """Response de ejecución de workflow"""
    success: bool
    execution_id: str
    message: str
    outputs: Dict[str, Any] = {}
    errors: List[str] = []

# Adaptador ComfyUI
class ComfyUIAdapter:
    """Adaptador para usar la lógica de ComfyUI en Emma Studio"""
    
    def __init__(self):
        self.available = COMFY_AVAILABLE
        if self.available:
            self.node_classes = nodes.NODE_CLASS_MAPPINGS
            self.node_display_names = nodes.NODE_DISPLAY_NAME_MAPPINGS
    
    def get_available_nodes(self) -> Dict[str, Any]:
        """Obtiene todos los nodos disponibles"""
        if not self.available:
            return {}
        
        node_info = {}
        for class_name, node_class in self.node_classes.items():
            try:
                input_types = node_class.INPUT_TYPES()
                return_types = getattr(node_class, 'RETURN_TYPES', ())
                category = getattr(node_class, 'CATEGORY', 'uncategorized')
                description = getattr(node_class, 'DESCRIPTION', '')
                
                node_info[class_name] = {
                    "display_name": self.node_display_names.get(class_name, class_name),
                    "category": category,
                    "description": description,
                    "input_types": input_types,
                    "return_types": return_types,
                    "function": getattr(node_class, 'FUNCTION', 'execute')
                }
            except Exception as e:
                logging.warning(f"Error procesando nodo {class_name}: {e}")
                continue
        
        return node_info
    
    def convert_visual_to_comfy(self, visual_workflow: VisualWorkflow) -> Dict[str, Any]:
        """Convierte workflow visual a formato ComfyUI"""
        comfy_workflow = {}
        
        # Convertir nodos
        for node in visual_workflow.nodes:
            comfy_workflow[node.id] = {
                "class_type": node.class_type,
                "inputs": node.inputs.copy()
            }
        
        # Aplicar conexiones
        for connection in visual_workflow.connections:
            target_node = comfy_workflow[connection.target_node]
            target_node["inputs"][connection.target_input] = [
                connection.source_node,
                connection.source_output
            ]
        
        return comfy_workflow
    
    def validate_workflow(self, workflow: Dict[str, Any]) -> List[str]:
        """Valida un workflow ComfyUI"""
        errors = []
        
        if not self.available:
            errors.append("ComfyUI no está disponible")
            return errors
        
        for node_id, node_data in workflow.items():
            class_type = node_data.get("class_type")
            if class_type not in self.node_classes:
                errors.append(f"Tipo de nodo desconocido: {class_type}")
                continue
            
            node_class = self.node_classes[class_type]
            try:
                input_types = node_class.INPUT_TYPES()
                inputs = node_data.get("inputs", {})
                
                # Validar inputs requeridos
                required_inputs = input_types.get("required", {})
                for input_name in required_inputs:
                    if input_name not in inputs:
                        errors.append(f"Nodo {node_id}: falta input requerido '{input_name}'")
                
            except Exception as e:
                errors.append(f"Error validando nodo {node_id}: {str(e)}")
        
        return errors

# Instancia global del adaptador
comfy_adapter = ComfyUIAdapter()

@router.get("/nodes")
async def get_available_nodes():
    """Obtiene todos los nodos disponibles de ComfyUI"""
    try:
        nodes_info = comfy_adapter.get_available_nodes()
        return {
            "success": True,
            "nodes": nodes_info,
            "total": len(nodes_info)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo nodos: {str(e)}")

@router.get("/nodes/categories")
async def get_node_categories():
    """Obtiene las categorías de nodos disponibles"""
    try:
        nodes_info = comfy_adapter.get_available_nodes()
        categories = {}
        
        for node_name, node_info in nodes_info.items():
            category = node_info.get("category", "uncategorized")
            if category not in categories:
                categories[category] = []
            categories[category].append({
                "name": node_name,
                "display_name": node_info.get("display_name", node_name),
                "description": node_info.get("description", "")
            })
        
        return {
            "success": True,
            "categories": categories
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error obteniendo categorías: {str(e)}")

@router.post("/convert")
async def convert_visual_workflow(visual_workflow: VisualWorkflow):
    """Convierte workflow visual a formato ComfyUI"""
    try:
        comfy_workflow = comfy_adapter.convert_visual_to_comfy(visual_workflow)
        
        return {
            "success": True,
            "comfy_workflow": comfy_workflow,
            "metadata": visual_workflow.metadata
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error convirtiendo workflow: {str(e)}")

@router.post("/validate")
async def validate_workflow(workflow_data: ComfyWorkflow):
    """Valida un workflow ComfyUI"""
    try:
        errors = comfy_adapter.validate_workflow(workflow_data.workflow)
        
        return {
            "success": len(errors) == 0,
            "valid": len(errors) == 0,
            "errors": errors
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error validando workflow: {str(e)}")

@router.post("/execute")
async def execute_workflow(
    request: WorkflowExecutionRequest,
    background_tasks: BackgroundTasks
):
    """Ejecuta un workflow ComfyUI"""
    try:
        if not comfy_adapter.available:
            raise HTTPException(status_code=503, detail="ComfyUI no está disponible")
        
        # Validar workflow
        errors = comfy_adapter.validate_workflow(request.workflow)
        if errors:
            return WorkflowExecutionResponse(
                success=False,
                execution_id="",
                message="Workflow inválido",
                errors=errors
            )
        
        # Por ahora, simulamos la ejecución
        # TODO: Implementar ejecución real con ComfyUI
        execution_id = f"exec_{hash(json.dumps(request.workflow, sort_keys=True))}"
        
        return WorkflowExecutionResponse(
            success=True,
            execution_id=execution_id,
            message="Workflow ejecutado exitosamente (simulado)",
            outputs={"message": "Ejecución simulada - implementar lógica real"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ejecutando workflow: {str(e)}")

@router.get("/templates")
async def get_workflow_templates():
    """Obtiene plantillas de workflows predefinidas"""
    templates = {
        "text_to_image_basic": {
            "name": "Text to Image Básico",
            "description": "Workflow básico para generar imágenes desde texto",
            "workflow": {
                "1": {
                    "class_type": "CLIPTextEncode",
                    "inputs": {
                        "text": "masterpiece, best quality, beautiful landscape",
                        "clip": ["2", 1]
                    }
                },
                "2": {
                    "class_type": "CheckpointLoaderSimple",
                    "inputs": {
                        "ckpt_name": "sd_xl_base_1.0.safetensors"
                    }
                },
                "3": {
                    "class_type": "EmptyLatentImage",
                    "inputs": {
                        "width": 1024,
                        "height": 1024,
                        "batch_size": 1
                    }
                },
                "4": {
                    "class_type": "KSampler",
                    "inputs": {
                        "seed": 42,
                        "steps": 20,
                        "cfg": 8.0,
                        "sampler_name": "euler",
                        "scheduler": "normal",
                        "denoise": 1.0,
                        "model": ["2", 0],
                        "positive": ["1", 0],
                        "negative": ["5", 0],
                        "latent_image": ["3", 0]
                    }
                },
                "5": {
                    "class_type": "CLIPTextEncode",
                    "inputs": {
                        "text": "bad quality, blurry, low resolution",
                        "clip": ["2", 1]
                    }
                },
                "6": {
                    "class_type": "VAEDecode",
                    "inputs": {
                        "samples": ["4", 0],
                        "vae": ["2", 2]
                    }
                },
                "7": {
                    "class_type": "SaveImage",
                    "inputs": {
                        "filename_prefix": "EmmaStudio",
                        "images": ["6", 0]
                    }
                }
            }
        }
    }
    
    return {
        "success": True,
        "templates": templates
    }

@router.get("/health")
async def health_check():
    """Verifica el estado del sistema ComfyUI"""
    return {
        "success": True,
        "comfy_available": comfy_adapter.available,
        "nodes_count": len(comfy_adapter.get_available_nodes()) if comfy_adapter.available else 0,
        "message": "ComfyUI Workflows API funcionando correctamente"
    }
