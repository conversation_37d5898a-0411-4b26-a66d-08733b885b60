"""
Emma Visual Workflows API - Sistema simplificado de workflows visuales
Ejecuta workflows paso a paso usando las APIs existentes de Emma Studio

El usuario solo selecciona herramientas, nosotros manejamos:
- APIs keys y configuraciones
- Llamadas a servicios externos
- Procesamiento y optimización
- Manejo de errores y reintentos
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()

# Modelos Pydantic
class WorkflowNode(BaseModel):
    """Nodo en el workflow"""
    id: str
    type: str
    inputs: Dict[str, Any]
    position: Dict[str, float]

class WorkflowEdge(BaseModel):
    """Conexión entre nodos"""
    source: str
    target: str
    sourceHandle: Optional[str] = None
    targetHandle: Optional[str] = None

class EmmaWorkflowRequest(BaseModel):
    """Request para ejecutar workflow Emma"""
    workflow: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = {}

class WorkflowExecutionResponse(BaseModel):
    """Response de ejecución"""
    success: bool
    execution_id: str
    message: str
    results: Dict[str, Any] = {}
    errors: List[str] = []

# Ejecutor de workflows Emma
class EmmaWorkflowExecutor:
    """Ejecuta workflows Emma paso a paso"""
    
    def __init__(self):
        self.node_executors = {
            'text-input': self._execute_text_input,
            'ideogram-generator': self._execute_ideogram_generator,
            'dalle-generator': self._execute_dalle_generator,
            'upscale': self._execute_upscale,
            'background-remover': self._execute_background_remover,
            'style-transfer': self._execute_style_transfer,
            'video-generator': self._execute_video_generator,
            'image-output': self._execute_image_output,
            'video-output': self._execute_video_output,
        }
    
    async def execute_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta un workflow completo"""
        try:
            nodes = workflow_data.get('nodes', [])
            edges = workflow_data.get('edges', [])
            
            # Construir grafo de dependencias
            dependency_graph = self._build_dependency_graph(nodes, edges)
            
            # Ejecutar nodos en orden topológico
            execution_order = self._topological_sort(dependency_graph)
            
            # Almacenar resultados de cada nodo
            node_results = {}
            
            for node_id in execution_order:
                node = next((n for n in nodes if n['id'] == node_id), None)
                if not node:
                    continue
                
                # Resolver inputs desde nodos anteriores
                resolved_inputs = self._resolve_node_inputs(node, node_results, edges)
                
                # Ejecutar nodo
                result = await self._execute_node(node, resolved_inputs)
                node_results[node_id] = result
                
                logging.info(f"Nodo {node_id} ejecutado: {result.get('success', False)}")
            
            return {
                'success': True,
                'execution_id': f"emma_exec_{int(datetime.now().timestamp())}",
                'message': 'Workflow ejecutado exitosamente',
                'results': node_results
            }
            
        except Exception as e:
            logging.error(f"Error ejecutando workflow: {e}")
            return {
                'success': False,
                'execution_id': '',
                'message': f'Error: {str(e)}',
                'results': {}
            }
    
    def _build_dependency_graph(self, nodes: List[Dict], edges: List[Dict]) -> Dict[str, List[str]]:
        """Construye grafo de dependencias"""
        graph = {node['id']: [] for node in nodes}
        
        for edge in edges:
            # target depende de source
            if edge['target'] in graph:
                graph[edge['target']].append(edge['source'])
        
        return graph
    
    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """Ordenamiento topológico para determinar orden de ejecución"""
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(node):
            if node in temp_visited:
                raise Exception("Ciclo detectado en el workflow")
            if node in visited:
                return
            
            temp_visited.add(node)
            for dependency in graph[node]:
                visit(dependency)
            temp_visited.remove(node)
            visited.add(node)
            result.append(node)
        
        for node in graph:
            if node not in visited:
                visit(node)
        
        return result
    
    def _resolve_node_inputs(self, node: Dict, node_results: Dict, edges: List[Dict]) -> Dict[str, Any]:
        """Resuelve inputs del nodo desde resultados de nodos anteriores"""
        resolved_inputs = node.get('inputs', {}).copy()
        
        # Buscar conexiones que alimentan este nodo
        for edge in edges:
            if edge['target'] == node['id']:
                source_id = edge['source']
                source_handle = edge.get('sourceHandle', 'output')
                target_handle = edge.get('targetHandle', 'input')
                
                # Obtener resultado del nodo fuente
                if source_id in node_results:
                    source_result = node_results[source_id]
                    if source_handle in source_result.get('outputs', {}):
                        resolved_inputs[target_handle] = source_result['outputs'][source_handle]
        
        return resolved_inputs
    
    async def _execute_node(self, node: Dict, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta un nodo individual"""
        node_type = node.get('type', 'unknown')
        
        if node_type in self.node_executors:
            return await self.node_executors[node_type](node, inputs)
        else:
            return {
                'success': False,
                'message': f'Tipo de nodo no soportado: {node_type}',
                'outputs': {}
            }
    
    # Ejecutores específicos para cada tipo de nodo
    async def _execute_text_input(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta nodo de entrada de texto"""
        text = inputs.get('text', node.get('inputs', {}).get('text', ''))
        
        return {
            'success': True,
            'message': 'Texto procesado',
            'outputs': {
                'text': text
            }
        }
    
    async def _execute_ideogram_generator(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta generación con Ideogram"""
        try:
            import requests
            
            prompt = inputs.get('prompt', '')
            model = inputs.get('model', 'ideogram-3.0')
            aspect_ratio = inputs.get('aspect_ratio', '1:1')
            style = inputs.get('style', 'auto')
            
            # Llamar a la API de Ideogram existente
            response = requests.post(
                "http://localhost:8000/api/image-generator/generate",
                json={
                    "prompt": prompt,
                    "model": model,
                    "aspect_ratio": aspect_ratio,
                    "style": style,
                    "magic_prompt_option": "AUTO"
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return {
                        'success': True,
                        'message': 'Imagen generada con Ideogram',
                        'outputs': {
                            'image': result.get('image_url'),
                            'url': result.get('image_url')
                        }
                    }
            
            return {
                'success': False,
                'message': 'Error generando imagen con Ideogram',
                'outputs': {}
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_dalle_generator(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta generación con DALL-E"""
        try:
            import requests
            
            prompt = inputs.get('prompt', '')
            model = inputs.get('model', 'dall-e-3')
            size = inputs.get('size', '1024x1024')
            quality = inputs.get('quality', 'standard')
            
            response = requests.post(
                "http://localhost:8000/api/v1/openai-images/generate",
                json={
                    "prompt": prompt,
                    "model": model,
                    "size": size,
                    "quality": quality,
                    "n": 1
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return {
                        'success': True,
                        'message': 'Imagen generada con DALL-E',
                        'outputs': {
                            'image': result.get('image_url'),
                            'url': result.get('image_url')
                        }
                    }
            
            return {
                'success': False,
                'message': 'Error generando imagen con DALL-E',
                'outputs': {}
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Error: {str(e)}',
                'outputs': {}
            }
    
    async def _execute_upscale(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta upscaling con Stability AI"""
        # TODO: Implementar llamada a API de upscaling
        return {
            'success': True,
            'message': 'Imagen mejorada (simulado)',
            'outputs': {
                'image': inputs.get('image', ''),
                'url': inputs.get('image', '')
            }
        }
    
    async def _execute_background_remover(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta remoción de fondo"""
        # TODO: Implementar llamada a API de background removal
        return {
            'success': True,
            'message': 'Fondo removido (simulado)',
            'outputs': {
                'image': inputs.get('image', ''),
                'url': inputs.get('image', '')
            }
        }
    
    async def _execute_style_transfer(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta transferencia de estilo"""
        # TODO: Implementar llamada a API de style transfer
        return {
            'success': True,
            'message': 'Estilo transferido (simulado)',
            'outputs': {
                'image': inputs.get('content_image', ''),
                'url': inputs.get('content_image', '')
            }
        }
    
    async def _execute_video_generator(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta generación de video"""
        # TODO: Implementar llamada a API de Luma Labs
        return {
            'success': True,
            'message': 'Video generado (simulado)',
            'outputs': {
                'video': 'https://example.com/video.mp4',
                'url': 'https://example.com/video.mp4'
            }
        }
    
    async def _execute_image_output(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta guardado de imagen"""
        return {
            'success': True,
            'message': 'Imagen guardada',
            'outputs': {
                'url': inputs.get('image', '')
            }
        }
    
    async def _execute_video_output(self, node: Dict, inputs: Dict) -> Dict[str, Any]:
        """Ejecuta guardado de video"""
        return {
            'success': True,
            'message': 'Video guardado',
            'outputs': {
                'url': inputs.get('video', '')
            }
        }

# Instancia global del ejecutor
workflow_executor = EmmaWorkflowExecutor()

@router.post("/execute")
async def execute_emma_workflow(request: EmmaWorkflowRequest):
    """Ejecuta un workflow Emma"""
    try:
        result = await workflow_executor.execute_workflow(request.workflow)
        
        return WorkflowExecutionResponse(
            success=result['success'],
            execution_id=result['execution_id'],
            message=result['message'],
            results=result['results'],
            errors=result.get('errors', [])
        )
        
    except Exception as e:
        logging.error(f"Error en execute_emma_workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Error ejecutando workflow: {str(e)}")

@router.get("/health")
async def health_check():
    """Verifica el estado del sistema de workflows Emma"""
    return {
        "success": True,
        "message": "Emma Workflows API funcionando correctamente",
        "available_nodes": len(workflow_executor.node_executors)
    }
