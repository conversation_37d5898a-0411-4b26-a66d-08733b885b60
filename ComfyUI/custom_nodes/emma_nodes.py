# Emma Studio Custom Nodes for ComfyUI
import sys
import os

# Agregar path de Emma Studio
emma_path = r"/Users/<USER>/emma-studio-"
sys.path.insert(0, emma_path)

try:
    from backend.app.emma_nodes import EMMA_NODE_CLASS_MAPPINGS, EMMA_NODE_DISPLAY_NAME_MAPPINGS
    
    # Registrar nodos Emma
    NODE_CLASS_MAPPINGS = EMMA_NODE_CLASS_MAPPINGS
    NODE_DISPLAY_NAME_MAPPINGS = EMMA_NODE_DISPLAY_NAME_MAPPINGS
    
    print("✅ Emma Studio nodes loaded successfully!")
    print(f"📦 Loaded {len(NODE_CLASS_MAPPINGS)} Emma nodes:")
    for node_name, display_name in NODE_DISPLAY_NAME_MAPPINGS.items():
        print(f"   - {display_name}")
    
except Exception as e:
    print(f"❌ Error loading Emma nodes: {e}")
    NODE_CLASS_MAPPINGS = {}
    NODE_DISPLAY_NAME_MAPPINGS = {}

__all__ = ["NODE_CLASS_MAPPINGS", "NODE_DISPLAY_NAME_MAPPINGS"]
