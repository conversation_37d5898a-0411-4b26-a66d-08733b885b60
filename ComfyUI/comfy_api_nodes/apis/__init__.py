# generated by datamodel-codegen:
#   filename:  filtered-openapi.yaml
#   timestamp: 2025-05-19T21:38:55+00:00

from __future__ import annotations

from datetime import date, datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union
from uuid import UUID

from pydantic import AnyUrl, BaseModel, ConfigDict, Field, RootModel, StrictBytes


class APIKey(BaseModel):
    created_at: Optional[datetime] = None
    description: Optional[str] = None
    id: Optional[str] = None
    key_prefix: Optional[str] = None
    name: Optional[str] = None


class APIKeyWithPlaintext(APIKey):
    plaintext_key: Optional[str] = Field(
        None, description='The full API key (only returned at creation)'
    )


class AuditLog(BaseModel):
    createdAt: Optional[datetime] = Field(
        None, description='The date and time the event was created'
    )
    event_id: Optional[str] = Field(None, description='the id of the event')
    event_type: Optional[str] = Field(None, description='the type of the event')
    params: Optional[Dict[str, Any]] = Field(
        None, description='data related to the event'
    )


class OutputFormat(str, Enum):
    jpeg = 'jpeg'
    png = 'png'


class BFLFluxPro11GenerateRequest(BaseModel):
    height: int = Field(..., description='Height of the generated image')
    image_prompt: Optional[str] = Field(None, description='Optional image prompt')
    output_format: Optional[OutputFormat] = Field(
        None, description='Output image format'
    )
    prompt: str = Field(..., description='The main text prompt for image generation')
    prompt_upsampling: Optional[bool] = Field(
        None, description='Whether to use prompt upsampling'
    )
    safety_tolerance: Optional[int] = Field(None, description='Safety tolerance level')
    seed: Optional[int] = Field(None, description='Random seed for reproducibility')
    webhook_secret: Optional[str] = Field(
        None, description='Optional webhook secret for async processing'
    )
    webhook_url: Optional[str] = Field(
        None, description='Optional webhook URL for async processing'
    )
    width: int = Field(..., description='Width of the generated image')


class BFLFluxPro11GenerateResponse(BaseModel):
    id: str = Field(..., description='Job ID for tracking')
    polling_url: str = Field(..., description='URL to poll for results')


class BFLFluxProGenerateRequest(BaseModel):
    guidance_scale: Optional[float] = Field(
        None, description='The guidance scale for generation.', ge=1.0, le=20.0
    )
    height: int = Field(
        ..., description='The height of the image to generate.', ge=64, le=2048
    )
    negative_prompt: Optional[str] = Field(
        None, description='The negative prompt for image generation.'
    )
    num_images: Optional[int] = Field(
        None, description='The number of images to generate.', ge=1, le=4
    )
    num_inference_steps: Optional[int] = Field(
        None, description='The number of inference steps.', ge=1, le=100
    )
    prompt: str = Field(..., description='The text prompt for image generation.')
    seed: Optional[int] = Field(None, description='The seed value for reproducibility.')
    width: int = Field(
        ..., description='The width of the image to generate.', ge=64, le=2048
    )


class BFLFluxProGenerateResponse(BaseModel):
    id: str = Field(..., description='The unique identifier for the generation task.')
    polling_url: str = Field(..., description='URL to poll for the generation result.')


class Status(str, Enum):
    in_progress = 'in_progress'
    completed = 'completed'
    incomplete = 'incomplete'


class Type(str, Enum):
    computer_call = 'computer_call'


class ComputerToolCall(BaseModel):
    action: Dict[str, Any]
    call_id: str = Field(
        ...,
        description='An identifier used when responding to the tool call with output.\n',
    )
    id: str = Field(..., description='The unique ID of the computer call.')
    status: Status = Field(
        ...,
        description='The status of the item. One of `in_progress`, `completed`, or\n`incomplete`. Populated when items are returned via API.\n',
    )
    type: Type = Field(
        ..., description='The type of the computer call. Always `computer_call`.'
    )


class Environment(str, Enum):
    windows = 'windows'
    mac = 'mac'
    linux = 'linux'
    ubuntu = 'ubuntu'
    browser = 'browser'


class Type1(str, Enum):
    computer_use_preview = 'computer_use_preview'


class ComputerUsePreviewTool(BaseModel):
    display_height: int = Field(..., description='The height of the computer display.')
    display_width: int = Field(..., description='The width of the computer display.')
    environment: Environment = Field(
        ..., description='The type of computer environment to control.'
    )
    type: Literal['ComputerUsePreviewTool'] = Field(
        ...,
        description='The type of the computer use tool. Always `computer_use_preview`.',
    )


class CreateAPIKeyRequest(BaseModel):
    description: Optional[str] = None
    name: str


class Customer(BaseModel):
    createdAt: Optional[datetime] = Field(
        None, description='The date and time the user was created'
    )
    email: Optional[str] = Field(None, description='The email address for this user')
    id: str = Field(..., description='The firebase UID of the user')
    is_admin: Optional[bool] = Field(None, description='Whether the user is an admin')
    metronome_id: Optional[str] = Field(None, description='The Metronome customer ID')
    name: Optional[str] = Field(None, description='The name for this user')
    stripe_id: Optional[str] = Field(None, description='The Stripe customer ID')
    updatedAt: Optional[datetime] = Field(
        None, description='The date and time the user was last updated'
    )


class CustomerStorageResourceResponse(BaseModel):
    download_url: Optional[str] = Field(
        None,
        description='The signed URL to use for downloading the file from the specified path',
    )
    existing_file: Optional[bool] = Field(
        None, description='Whether an existing file with the same hash was found'
    )
    expires_at: Optional[datetime] = Field(
        None, description='When the signed URL will expire'
    )
    upload_url: Optional[str] = Field(
        None,
        description='The signed URL to use for uploading the file to the specified path',
    )


class Role(str, Enum):
    user = 'user'
    assistant = 'assistant'
    system = 'system'
    developer = 'developer'


class Type2(str, Enum):
    message = 'message'


class ErrorResponse(BaseModel):
    error: str
    message: str


class Type3(str, Enum):
    file_search = 'file_search'


class FileSearchTool(BaseModel):
    type: Literal['FileSearchTool'] = Field(..., description='The type of tool')
    vector_store_ids: List[str] = Field(
        ..., description='IDs of vector stores to search in'
    )


class Result(BaseModel):
    file_id: Optional[str] = Field(None, description='The unique ID of the file.\n')
    filename: Optional[str] = Field(None, description='The name of the file.\n')
    score: Optional[float] = Field(
        None, description='The relevance score of the file - a value between 0 and 1.\n'
    )
    text: Optional[str] = Field(
        None, description='The text that was retrieved from the file.\n'
    )


class Status1(str, Enum):
    in_progress = 'in_progress'
    searching = 'searching'
    completed = 'completed'
    incomplete = 'incomplete'
    failed = 'failed'


class Type4(str, Enum):
    file_search_call = 'file_search_call'


class FileSearchToolCall(BaseModel):
    id: str = Field(..., description='The unique ID of the file search tool call.\n')
    queries: List[str] = Field(
        ..., description='The queries used to search for files.\n'
    )
    results: Optional[List[Result]] = Field(
        None, description='The results of the file search tool call.\n'
    )
    status: Status1 = Field(
        ...,
        description='The status of the file search tool call. One of `in_progress`, \n`searching`, `incomplete` or `failed`,\n',
    )
    type: Type4 = Field(
        ...,
        description='The type of the file search tool call. Always `file_search_call`.\n',
    )


class Type5(str, Enum):
    function = 'function'


class FunctionTool(BaseModel):
    description: Optional[str] = Field(
        None, description='Description of what the function does'
    )
    name: str = Field(..., description='Name of the function')
    parameters: Dict[str, Any] = Field(
        ..., description='JSON Schema object describing the function parameters'
    )
    type: Literal['FunctionTool'] = Field(..., description='The type of tool')


class Status2(str, Enum):
    in_progress = 'in_progress'
    completed = 'completed'
    incomplete = 'incomplete'


class Type6(str, Enum):
    function_call = 'function_call'


class FunctionToolCall(BaseModel):
    arguments: str = Field(
        ..., description='A JSON string of the arguments to pass to the function.\n'
    )
    call_id: str = Field(
        ...,
        description='The unique ID of the function tool call generated by the model.\n',
    )
    id: Optional[str] = Field(
        None, description='The unique ID of the function tool call.\n'
    )
    name: str = Field(..., description='The name of the function to run.\n')
    status: Optional[Status2] = Field(
        None,
        description='The status of the item. One of `in_progress`, `completed`, or\n`incomplete`. Populated when items are returned via API.\n',
    )
    type: Type6 = Field(
        ..., description='The type of the function tool call. Always `function_call`.\n'
    )


class GeminiCitation(BaseModel):
    authors: Optional[List[str]] = None
    endIndex: Optional[int] = None
    license: Optional[str] = None
    publicationDate: Optional[date] = None
    startIndex: Optional[int] = None
    title: Optional[str] = None
    uri: Optional[str] = None


class GeminiCitationMetadata(BaseModel):
    citations: Optional[List[GeminiCitation]] = None


class Role1(str, Enum):
    user = 'user'
    model = 'model'


class GeminiFunctionDeclaration(BaseModel):
    description: Optional[str] = None
    name: str
    parameters: Dict[str, Any] = Field(
        ..., description='JSON schema for the function parameters'
    )


class GeminiGenerationConfig(BaseModel):
    maxOutputTokens: Optional[int] = Field(
        None,
        description='Maximum number of tokens that can be generated in the response. A token is approximately 4 characters. 100 tokens correspond to roughly 60-80 words.\n',
        examples=[2048],
        ge=16,
        le=8192,
    )
    seed: Optional[int] = Field(
        None,
        description="When seed is fixed to a specific value, the model makes a best effort to provide the same response for repeated requests. Deterministic output isn't guaranteed. Also, changing the model or parameter settings, such as the temperature, can cause variations in the response even when you use the same seed value. By default, a random seed value is used. Available for the following models:, gemini-2.5-flash-preview-04-1, gemini-2.5-pro-preview-05-0, gemini-2.0-flash-lite-00, gemini-2.0-flash-001\n",
        examples=[343940597],
    )
    stopSequences: Optional[List[str]] = None
    temperature: Optional[float] = Field(
        1,
        description="The temperature is used for sampling during response generation, which occurs when topP and topK are applied. Temperature controls the degree of randomness in token selection. Lower temperatures are good for prompts that require a less open-ended or creative response, while higher temperatures can lead to more diverse or creative results. A temperature of 0 means that the highest probability tokens are always selected. In this case, responses for a given prompt are mostly deterministic, but a small amount of variation is still possible. If the model returns a response that's too generic, too short, or the model gives a fallback response, try increasing the temperature\n",
        ge=0.0,
        le=2.0,
    )
    topK: Optional[int] = Field(
        40,
        description="Top-K changes how the model selects tokens for output. A top-K of 1 means the next selected token is the most probable among all tokens in the model's vocabulary. A top-K of 3 means that the next token is selected from among the 3 most probable tokens by using temperature.\n",
        examples=[40],
        ge=1,
    )
    topP: Optional[float] = Field(
        0.95,
        description='If specified, nucleus sampling is used.\nTop-P changes how the model selects tokens for output. Tokens are selected from the most (see top-K) to least probable until the sum of their probabilities equals the top-P value. For example, if tokens A, B, and C have a probability of 0.3, 0.2, and 0.1 and the top-P value is 0.5, then the model will select either A or B as the next token by using temperature and excludes C as a candidate.\nSpecify a lower value for less random responses and a higher value for more random responses.\n',
        ge=0.0,
        le=1.0,
    )


class GeminiMimeType(str, Enum):
    application_pdf = 'application/pdf'
    audio_mpeg = 'audio/mpeg'
    audio_mp3 = 'audio/mp3'
    audio_wav = 'audio/wav'
    image_png = 'image/png'
    image_jpeg = 'image/jpeg'
    image_webp = 'image/webp'
    text_plain = 'text/plain'
    video_mov = 'video/mov'
    video_mpeg = 'video/mpeg'
    video_mp4 = 'video/mp4'
    video_mpg = 'video/mpg'
    video_avi = 'video/avi'
    video_wmv = 'video/wmv'
    video_mpegps = 'video/mpegps'
    video_flv = 'video/flv'


class GeminiOffset(BaseModel):
    nanos: Optional[int] = Field(
        None,
        description='Signed fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values.\n',
        examples=[0],
        ge=0,
        le=999999999,
    )
    seconds: Optional[int] = Field(
        None,
        description='Signed seconds of the span of time. Must be from -315,576,000,000 to +315,576,000,000 inclusive.\n',
        examples=[60],
        ge=-315576000000,
        le=315576000000,
    )


class GeminiSafetyCategory(str, Enum):
    HARM_CATEGORY_SEXUALLY_EXPLICIT = 'HARM_CATEGORY_SEXUALLY_EXPLICIT'
    HARM_CATEGORY_HATE_SPEECH = 'HARM_CATEGORY_HATE_SPEECH'
    HARM_CATEGORY_HARASSMENT = 'HARM_CATEGORY_HARASSMENT'
    HARM_CATEGORY_DANGEROUS_CONTENT = 'HARM_CATEGORY_DANGEROUS_CONTENT'


class Probability(str, Enum):
    NEGLIGIBLE = 'NEGLIGIBLE'
    LOW = 'LOW'
    MEDIUM = 'MEDIUM'
    HIGH = 'HIGH'
    UNKNOWN = 'UNKNOWN'


class GeminiSafetyRating(BaseModel):
    category: Optional[GeminiSafetyCategory] = None
    probability: Optional[Probability] = Field(
        None,
        description='The probability that the content violates the specified safety category',
    )


class GeminiSafetyThreshold(str, Enum):
    OFF = 'OFF'
    BLOCK_NONE = 'BLOCK_NONE'
    BLOCK_LOW_AND_ABOVE = 'BLOCK_LOW_AND_ABOVE'
    BLOCK_MEDIUM_AND_ABOVE = 'BLOCK_MEDIUM_AND_ABOVE'
    BLOCK_ONLY_HIGH = 'BLOCK_ONLY_HIGH'


class GeminiTextPart(BaseModel):
    text: Optional[str] = Field(
        None,
        description='A text prompt or code snippet.',
        examples=['Answer as concisely as possible'],
    )


class GeminiTool(BaseModel):
    functionDeclarations: Optional[List[GeminiFunctionDeclaration]] = None


class GeminiVideoMetadata(BaseModel):
    endOffset: Optional[GeminiOffset] = None
    startOffset: Optional[GeminiOffset] = None


class IdeogramColorPalette1(BaseModel):
    name: str = Field(..., description='Name of the preset color palette')


class Member(BaseModel):
    color: Optional[str] = Field(
        None, description='Hexadecimal color code', pattern='^#[0-9A-Fa-f]{6}$'
    )
    weight: Optional[float] = Field(
        None, description='Optional weight for the color (0-1)', ge=0.0, le=1.0
    )


class IdeogramColorPalette2(BaseModel):
    members: List[Member] = Field(
        ..., description='Array of color definitions with optional weights'
    )


class IdeogramColorPalette(
    RootModel[Union[IdeogramColorPalette1, IdeogramColorPalette2]]
):
    root: Union[IdeogramColorPalette1, IdeogramColorPalette2] = Field(
        ...,
        description='A color palette specification that can either use a preset name or explicit color definitions with weights',
    )


class ImageRequest(BaseModel):
    aspect_ratio: Optional[str] = Field(
        None,
        description="Optional. The aspect ratio (e.g., 'ASPECT_16_9', 'ASPECT_1_1'). Cannot be used with resolution. Defaults to 'ASPECT_1_1' if unspecified.",
    )
    color_palette: Optional[Dict[str, Any]] = Field(
        None, description='Optional. Color palette object. Only for V_2, V_2_TURBO.'
    )
    magic_prompt_option: Optional[str] = Field(
        None, description="Optional. MagicPrompt usage ('AUTO', 'ON', 'OFF')."
    )
    model: str = Field(..., description="The model used (e.g., 'V_2', 'V_2A_TURBO')")
    negative_prompt: Optional[str] = Field(
        None,
        description='Optional. Description of what to exclude. Only for V_1, V_1_TURBO, V_2, V_2_TURBO.',
    )
    num_images: Optional[int] = Field(
        1,
        description='Optional. Number of images to generate (1-8). Defaults to 1.',
        ge=1,
        le=8,
    )
    prompt: str = Field(
        ..., description='Required. The prompt to use to generate the image.'
    )
    resolution: Optional[str] = Field(
        None,
        description="Optional. Resolution (e.g., 'RESOLUTION_1024_1024'). Only for model V_2. Cannot be used with aspect_ratio.",
    )
    seed: Optional[int] = Field(
        None,
        description='Optional. A number between 0 and 2147483647.',
        ge=0,
        le=2147483647,
    )
    style_type: Optional[str] = Field(
        None,
        description="Optional. Style type ('AUTO', 'GENERAL', 'REALISTIC', 'DESIGN', 'RENDER_3D', 'ANIME'). Only for models V_2 and above.",
    )


class IdeogramGenerateRequest(BaseModel):
    image_request: ImageRequest = Field(
        ..., description='The image generation request parameters.'
    )


class Datum(BaseModel):
    is_image_safe: Optional[bool] = Field(
        None, description='Indicates whether the image is considered safe.'
    )
    prompt: Optional[str] = Field(
        None, description='The prompt used to generate this image.'
    )
    resolution: Optional[str] = Field(
        None, description="The resolution of the generated image (e.g., '1024x1024')."
    )
    seed: Optional[int] = Field(
        None, description='The seed value used for this generation.'
    )
    style_type: Optional[str] = Field(
        None,
        description="The style type used for generation (e.g., 'REALISTIC', 'ANIME').",
    )
    url: Optional[str] = Field(None, description='URL to the generated image.')


class IdeogramGenerateResponse(BaseModel):
    created: Optional[datetime] = Field(
        None, description='Timestamp when the generation was created.'
    )
    data: Optional[List[Datum]] = Field(
        None, description='Array of generated image information.'
    )


class StyleCode(RootModel[str]):
    root: str = Field(..., pattern='^[0-9A-Fa-f]{8}$')


class Datum1(BaseModel):
    is_image_safe: Optional[bool] = None
    prompt: Optional[str] = None
    resolution: Optional[str] = None
    seed: Optional[int] = None
    style_type: Optional[str] = None
    url: Optional[str] = None


class IdeogramV3IdeogramResponse(BaseModel):
    created: Optional[datetime] = None
    data: Optional[List[Datum1]] = None


class RenderingSpeed1(str, Enum):
    TURBO = 'TURBO'
    DEFAULT = 'DEFAULT'
    QUALITY = 'QUALITY'


class IdeogramV3ReframeRequest(BaseModel):
    color_palette: Optional[Dict[str, Any]] = None
    image: Optional[StrictBytes] = None
    num_images: Optional[int] = Field(None, ge=1, le=8)
    rendering_speed: Optional[RenderingSpeed1] = None
    resolution: str
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    style_codes: Optional[List[str]] = None
    style_reference_images: Optional[List[StrictBytes]] = None


class MagicPrompt(str, Enum):
    AUTO = 'AUTO'
    ON = 'ON'
    OFF = 'OFF'


class StyleType(str, Enum):
    AUTO = 'AUTO'
    GENERAL = 'GENERAL'
    REALISTIC = 'REALISTIC'
    DESIGN = 'DESIGN'


class IdeogramV3RemixRequest(BaseModel):
    aspect_ratio: Optional[str] = None
    color_palette: Optional[Dict[str, Any]] = None
    image: Optional[StrictBytes] = None
    image_weight: Optional[int] = Field(50, ge=1, le=100)
    magic_prompt: Optional[MagicPrompt] = None
    negative_prompt: Optional[str] = None
    num_images: Optional[int] = Field(None, ge=1, le=8)
    prompt: str
    rendering_speed: Optional[RenderingSpeed1] = None
    resolution: Optional[str] = None
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    style_codes: Optional[List[str]] = None
    style_reference_images: Optional[List[StrictBytes]] = None
    style_type: Optional[StyleType] = None


class IdeogramV3ReplaceBackgroundRequest(BaseModel):
    color_palette: Optional[Dict[str, Any]] = None
    image: Optional[StrictBytes] = None
    magic_prompt: Optional[MagicPrompt] = None
    num_images: Optional[int] = Field(None, ge=1, le=8)
    prompt: str
    rendering_speed: Optional[RenderingSpeed1] = None
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    style_codes: Optional[List[str]] = None
    style_reference_images: Optional[List[StrictBytes]] = None


class ColorPalette(BaseModel):
    name: str = Field(..., description='Name of the color palette', examples=['PASTEL'])


class MagicPrompt2(str, Enum):
    ON = 'ON'
    OFF = 'OFF'


class StyleType1(str, Enum):
    GENERAL = 'GENERAL'


class ImagenImageGenerationInstance(BaseModel):
    prompt: str = Field(..., description='Text prompt for image generation')


class AspectRatio(str, Enum):
    field_1_1 = '1:1'
    field_9_16 = '9:16'
    field_16_9 = '16:9'
    field_3_4 = '3:4'
    field_4_3 = '4:3'


class PersonGeneration(str, Enum):
    dont_allow = 'dont_allow'
    allow_adult = 'allow_adult'
    allow_all = 'allow_all'


class SafetySetting(str, Enum):
    block_most = 'block_most'
    block_some = 'block_some'
    block_few = 'block_few'
    block_fewest = 'block_fewest'


class ImagenImagePrediction(BaseModel):
    bytesBase64Encoded: Optional[str] = Field(
        None, description='Base64-encoded image content'
    )
    mimeType: Optional[str] = Field(
        None, description='MIME type of the generated image'
    )
    prompt: Optional[str] = Field(
        None, description='Enhanced or rewritten prompt used to generate this image'
    )


class MimeType(str, Enum):
    image_png = 'image/png'
    image_jpeg = 'image/jpeg'


class ImagenOutputOptions(BaseModel):
    compressionQuality: Optional[int] = Field(None, ge=0, le=100)
    mimeType: Optional[MimeType] = None


class Includable(str, Enum):
    file_search_call_results = 'file_search_call.results'
    message_input_image_image_url = 'message.input_image.image_url'
    computer_call_output_output_image_url = 'computer_call_output.output.image_url'


class Type7(str, Enum):
    input_file = 'input_file'


class InputFileContent(BaseModel):
    file_data: Optional[str] = Field(
        None, description='The content of the file to be sent to the model.\n'
    )
    file_id: Optional[str] = Field(
        None, description='The ID of the file to be sent to the model.'
    )
    filename: Optional[str] = Field(
        None, description='The name of the file to be sent to the model.'
    )
    type: Type7 = Field(
        ..., description='The type of the input item. Always `input_file`.'
    )


class Detail(str, Enum):
    low = 'low'
    high = 'high'
    auto = 'auto'


class Type8(str, Enum):
    input_image = 'input_image'


class InputImageContent(BaseModel):
    detail: Detail = Field(
        ...,
        description='The detail level of the image to be sent to the model. One of `high`, `low`, or `auto`. Defaults to `auto`.',
    )
    file_id: Optional[str] = Field(
        None, description='The ID of the file to be sent to the model.'
    )
    image_url: Optional[str] = Field(
        None,
        description='The URL of the image to be sent to the model. A fully qualified URL or base64 encoded image in a data URL.',
    )
    type: Type8 = Field(
        ..., description='The type of the input item. Always `input_image`.'
    )


class Role3(str, Enum):
    user = 'user'
    system = 'system'
    developer = 'developer'


class Type9(str, Enum):
    message = 'message'


class Type10(str, Enum):
    input_text = 'input_text'


class InputTextContent(BaseModel):
    text: str = Field(..., description='The text input to the model.')
    type: Type10 = Field(
        ..., description='The type of the input item. Always `input_text`.'
    )


class KlingAudioUploadType(str, Enum):
    file = 'file'
    url = 'url'


class KlingCameraConfig(BaseModel):
    horizontal: Optional[float] = Field(
        None,
        description="Controls camera's movement along horizontal axis (x-axis). Negative indicates left, positive indicates right.",
        ge=-10.0,
        le=10.0,
    )
    pan: Optional[float] = Field(
        None,
        description="Controls camera's rotation in vertical plane (x-axis). Negative indicates downward rotation, positive indicates upward rotation.",
        ge=-10.0,
        le=10.0,
    )
    roll: Optional[float] = Field(
        None,
        description="Controls camera's rolling amount (z-axis). Negative indicates counterclockwise, positive indicates clockwise.",
        ge=-10.0,
        le=10.0,
    )
    tilt: Optional[float] = Field(
        None,
        description="Controls camera's rotation in horizontal plane (y-axis). Negative indicates left rotation, positive indicates right rotation.",
        ge=-10.0,
        le=10.0,
    )
    vertical: Optional[float] = Field(
        None,
        description="Controls camera's movement along vertical axis (y-axis). Negative indicates downward, positive indicates upward.",
        ge=-10.0,
        le=10.0,
    )
    zoom: Optional[float] = Field(
        None,
        description="Controls change in camera's focal length. Negative indicates narrower field of view, positive indicates wider field of view.",
        ge=-10.0,
        le=10.0,
    )


class KlingCameraControlType(str, Enum):
    simple = 'simple'
    down_back = 'down_back'
    forward_up = 'forward_up'
    right_turn_forward = 'right_turn_forward'
    left_turn_forward = 'left_turn_forward'


class KlingCharacterEffectModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_5 = 'kling-v1-5'
    kling_v1_6 = 'kling-v1-6'


class KlingDualCharacterEffectsScene(str, Enum):
    hug = 'hug'
    kiss = 'kiss'
    heart_gesture = 'heart_gesture'


class KlingDualCharacterImages(RootModel[List[str]]):
    root: List[str] = Field(..., max_length=2, min_length=2)


class KlingErrorResponse(BaseModel):
    code: int = Field(
        ...,
        description='- 1000: Authentication failed\n- 1001: Authorization is empty\n- 1002: Authorization is invalid\n- 1003: Authorization is not yet valid\n- 1004: Authorization has expired\n- 1100: Account exception\n- 1101: Account in arrears (postpaid scenario)\n- 1102: Resource pack depleted or expired (prepaid scenario)\n- 1103: Unauthorized access to requested resource\n- 1200: Invalid request parameters\n- 1201: Invalid parameters\n- 1202: Invalid request method\n- 1203: Requested resource does not exist\n- 1300: Trigger platform strategy\n- 1301: Trigger content security policy\n- 1302: API request too frequent\n- 1303: Concurrency/QPS exceeds limit\n- 1304: Trigger IP whitelist policy\n- 5000: Internal server error\n- 5001: Service temporarily unavailable\n- 5002: Server internal timeout\n',
    )
    message: str = Field(..., description='Human-readable error message')
    request_id: str = Field(
        ..., description='Request ID for tracking and troubleshooting'
    )


class Trajectory(BaseModel):
    x: Optional[int] = Field(
        None,
        description='The horizontal coordinate of trajectory point. Based on bottom-left corner of image as origin (0,0).',
    )
    y: Optional[int] = Field(
        None,
        description='The vertical coordinate of trajectory point. Based on bottom-left corner of image as origin (0,0).',
    )


class DynamicMask(BaseModel):
    mask: Optional[AnyUrl] = Field(
        None,
        description='Dynamic Brush Application Area (Mask image created by users using the motion brush). The aspect ratio must match the input image.',
    )
    trajectories: Optional[List[Trajectory]] = None


class TaskInfo(BaseModel):
    external_task_id: Optional[str] = None


class KlingImageGenAspectRatio(str, Enum):
    field_16_9 = '16:9'
    field_9_16 = '9:16'
    field_1_1 = '1:1'
    field_4_3 = '4:3'
    field_3_4 = '3:4'
    field_3_2 = '3:2'
    field_2_3 = '2:3'
    field_21_9 = '21:9'


class KlingImageGenImageReferenceType(str, Enum):
    subject = 'subject'
    face = 'face'


class KlingImageGenModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_5 = 'kling-v1-5'
    kling_v2 = 'kling-v2'


class KlingImageGenerationsRequest(BaseModel):
    aspect_ratio: Optional[KlingImageGenAspectRatio] = '16:9'
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback notification address'
    )
    human_fidelity: Optional[float] = Field(
        0.45, description='Subject reference similarity', ge=0.0, le=1.0
    )
    image: Optional[str] = Field(
        None, description='Reference Image - Base64 encoded string or image URL'
    )
    image_fidelity: Optional[float] = Field(
        0.5, description='Reference intensity for user-uploaded images', ge=0.0, le=1.0
    )
    image_reference: Optional[KlingImageGenImageReferenceType] = None
    model_name: Optional[KlingImageGenModelName] = 'kling-v1'
    n: Optional[int] = Field(1, description='Number of generated images', ge=1, le=9)
    negative_prompt: Optional[str] = Field(
        None, description='Negative text prompt', max_length=200
    )
    prompt: str = Field(..., description='Positive text prompt', max_length=500)


class KlingImageResult(BaseModel):
    index: Optional[int] = Field(None, description='Image Number (0-9)')
    url: Optional[AnyUrl] = Field(None, description='URL for generated image')


class KlingLipSyncMode(str, Enum):
    text2video = 'text2video'
    audio2video = 'audio2video'


class KlingLipSyncVoiceLanguage(str, Enum):
    zh = 'zh'
    en = 'en'


class ResourcePackType(str, Enum):
    decreasing_total = 'decreasing_total'
    constant_period = 'constant_period'


class Status4(str, Enum):
    toBeOnline = 'toBeOnline'
    online = 'online'
    expired = 'expired'
    runOut = 'runOut'


class ResourcePackSubscribeInfo(BaseModel):
    effective_time: Optional[int] = Field(
        None, description='Effective time, Unix timestamp in ms'
    )
    invalid_time: Optional[int] = Field(
        None, description='Expiration time, Unix timestamp in ms'
    )
    purchase_time: Optional[int] = Field(
        None, description='Purchase time, Unix timestamp in ms'
    )
    remaining_quantity: Optional[float] = Field(
        None, description='Remaining quantity (updated with a 12-hour delay)'
    )
    resource_pack_id: Optional[str] = Field(None, description='Resource package ID')
    resource_pack_name: Optional[str] = Field(None, description='Resource package name')
    resource_pack_type: Optional[ResourcePackType] = Field(
        None,
        description='Resource package type (decreasing_total=decreasing total, constant_period=constant periodicity)',
    )
    status: Optional[Status4] = Field(None, description='Resource Package Status')
    total_quantity: Optional[float] = Field(None, description='Total quantity')


class Data3(BaseModel):
    code: Optional[int] = Field(None, description='Error code; 0 indicates success')
    msg: Optional[str] = Field(None, description='Error information')
    resource_pack_subscribe_infos: Optional[List[ResourcePackSubscribeInfo]] = Field(
        None, description='Resource package list'
    )


class KlingResourcePackageResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code; 0 indicates success')
    data: Optional[Data3] = None
    message: Optional[str] = Field(None, description='Error information')
    request_id: Optional[str] = Field(
        None,
        description='Request ID, generated by the system, used to track requests and troubleshoot problems',
    )


class KlingSingleImageEffectDuration(str, Enum):
    field_5 = '5'


class KlingSingleImageEffectModelName(str, Enum):
    kling_v1_6 = 'kling-v1-6'


class KlingSingleImageEffectsScene(str, Enum):
    bloombloom = 'bloombloom'
    dizzydizzy = 'dizzydizzy'
    fuzzyfuzzy = 'fuzzyfuzzy'
    squish = 'squish'
    expansion = 'expansion'


class KlingTaskStatus(str, Enum):
    submitted = 'submitted'
    processing = 'processing'
    succeed = 'succeed'
    failed = 'failed'


class KlingTextToVideoModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_6 = 'kling-v1-6'


class KlingVideoGenAspectRatio(str, Enum):
    field_16_9 = '16:9'
    field_9_16 = '9:16'
    field_1_1 = '1:1'


class KlingVideoGenCfgScale(RootModel[float]):
    root: float = Field(
        ...,
        description="Flexibility in video generation. The higher the value, the lower the model's degree of flexibility, and the stronger the relevance to the user's prompt.",
        ge=0.0,
        le=1.0,
    )


class KlingVideoGenDuration(str, Enum):
    field_5 = '5'
    field_10 = '10'


class KlingVideoGenMode(str, Enum):
    std = 'std'
    pro = 'pro'


class KlingVideoGenModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_5 = 'kling-v1-5'
    kling_v1_6 = 'kling-v1-6'
    kling_v2_master = 'kling-v2-master'


class KlingVideoResult(BaseModel):
    duration: Optional[str] = Field(None, description='Total video duration')
    id: Optional[str] = Field(None, description='Generated video ID')
    url: Optional[AnyUrl] = Field(None, description='URL for generated video')


class KlingVirtualTryOnModelName(str, Enum):
    kolors_virtual_try_on_v1 = 'kolors-virtual-try-on-v1'
    kolors_virtual_try_on_v1_5 = 'kolors-virtual-try-on-v1-5'


class KlingVirtualTryOnRequest(BaseModel):
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback notification address'
    )
    cloth_image: Optional[str] = Field(
        None,
        description='Reference clothing image - Base64 encoded string or image URL',
    )
    human_image: str = Field(
        ..., description='Reference human image - Base64 encoded string or image URL'
    )
    model_name: Optional[KlingVirtualTryOnModelName] = 'kolors-virtual-try-on-v1'


class TaskResult6(BaseModel):
    images: Optional[List[KlingImageResult]] = None


class Data7(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_result: Optional[TaskResult6] = None
    task_status: Optional[KlingTaskStatus] = None
    task_status_msg: Optional[str] = Field(None, description='Task status information')
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingVirtualTryOnResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data7] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class LumaAspectRatio(str, Enum):
    field_1_1 = '1:1'
    field_16_9 = '16:9'
    field_9_16 = '9:16'
    field_4_3 = '4:3'
    field_3_4 = '3:4'
    field_21_9 = '21:9'
    field_9_21 = '9:21'


class LumaAssets(BaseModel):
    image: Optional[AnyUrl] = Field(None, description='The URL of the image')
    progress_video: Optional[AnyUrl] = Field(
        None, description='The URL of the progress video'
    )
    video: Optional[AnyUrl] = Field(None, description='The URL of the video')


class GenerationType(str, Enum):
    add_audio = 'add_audio'


class LumaAudioGenerationRequest(BaseModel):
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback URL for the audio'
    )
    generation_type: Optional[GenerationType] = 'add_audio'
    negative_prompt: Optional[str] = Field(
        None, description='The negative prompt of the audio'
    )
    prompt: Optional[str] = Field(None, description='The prompt of the audio')


class LumaError(BaseModel):
    detail: Optional[str] = Field(None, description='The error message')


class Type11(str, Enum):
    generation = 'generation'


class LumaGenerationReference(BaseModel):
    id: UUID = Field(..., description='The ID of the generation')
    type: Literal['generation']


class GenerationType1(str, Enum):
    video = 'video'


class LumaGenerationType(str, Enum):
    video = 'video'
    image = 'image'


class GenerationType2(str, Enum):
    image = 'image'


class LumaImageIdentity(BaseModel):
    images: Optional[List[AnyUrl]] = Field(
        None, description='The URLs of the image identity'
    )


class LumaImageModel(str, Enum):
    photon_1 = 'photon-1'
    photon_flash_1 = 'photon-flash-1'


class LumaImageRef(BaseModel):
    url: Optional[AnyUrl] = Field(None, description='The URL of the image reference')
    weight: Optional[float] = Field(
        None, description='The weight of the image reference'
    )


class Type12(str, Enum):
    image = 'image'


class LumaImageReference(BaseModel):
    type: Literal['image']
    url: AnyUrl = Field(..., description='The URL of the image')


class LumaKeyframe(RootModel[Union[LumaGenerationReference, LumaImageReference]]):
    root: Union[LumaGenerationReference, LumaImageReference] = Field(
        ...,
        description='A keyframe can be either a Generation reference, an Image, or a Video',
        discriminator='type',
    )


class LumaKeyframes(BaseModel):
    frame0: Optional[LumaKeyframe] = None
    frame1: Optional[LumaKeyframe] = None


class LumaModifyImageRef(BaseModel):
    url: Optional[AnyUrl] = Field(None, description='The URL of the image reference')
    weight: Optional[float] = Field(
        None, description='The weight of the modify image reference'
    )


class LumaState(str, Enum):
    queued = 'queued'
    dreaming = 'dreaming'
    completed = 'completed'
    failed = 'failed'


class GenerationType3(str, Enum):
    upscale_video = 'upscale_video'


class LumaVideoModel(str, Enum):
    ray_2 = 'ray-2'
    ray_flash_2 = 'ray-flash-2'
    ray_1_6 = 'ray-1-6'


class LumaVideoModelOutputDuration1(str, Enum):
    field_5s = '5s'
    field_9s = '9s'


class LumaVideoModelOutputDuration(
    RootModel[Union[LumaVideoModelOutputDuration1, str]]
):
    root: Union[LumaVideoModelOutputDuration1, str]


class LumaVideoModelOutputResolution1(str, Enum):
    field_540p = '540p'
    field_720p = '720p'
    field_1080p = '1080p'
    field_4k = '4k'


class LumaVideoModelOutputResolution(
    RootModel[Union[LumaVideoModelOutputResolution1, str]]
):
    root: Union[LumaVideoModelOutputResolution1, str]


class MinimaxBaseResponse(BaseModel):
    status_code: int = Field(
        ...,
        description='Status code. 0 indicates success, other values indicate errors.',
    )
    status_msg: str = Field(
        ..., description='Specific error details or success message.'
    )


class File(BaseModel):
    bytes: Optional[int] = Field(None, description='File size in bytes')
    created_at: Optional[int] = Field(
        None, description='Unix timestamp when the file was created, in seconds'
    )
    download_url: Optional[str] = Field(
        None, description='The URL to download the video'
    )
    file_id: Optional[int] = Field(None, description='Unique identifier for the file')
    filename: Optional[str] = Field(None, description='The name of the file')
    purpose: Optional[str] = Field(None, description='The purpose of using the file')


class MinimaxFileRetrieveResponse(BaseModel):
    base_resp: MinimaxBaseResponse
    file: File


class Status5(str, Enum):
    Queueing = 'Queueing'
    Preparing = 'Preparing'
    Processing = 'Processing'
    Success = 'Success'
    Fail = 'Fail'


class MinimaxTaskResultResponse(BaseModel):
    base_resp: MinimaxBaseResponse
    file_id: Optional[str] = Field(
        None,
        description='After the task status changes to Success, this field returns the file ID corresponding to the generated video.',
    )
    status: Status5 = Field(
        ...,
        description="Task status: 'Queueing' (in queue), 'Preparing' (task is preparing), 'Processing' (generating), 'Success' (task completed successfully), or 'Fail' (task failed).",
    )
    task_id: str = Field(..., description='The task ID being queried.')


class Model(str, Enum):
    T2V_01_Director = 'T2V-01-Director'
    I2V_01_Director = 'I2V-01-Director'
    S2V_01 = 'S2V-01'
    I2V_01 = 'I2V-01'
    I2V_01_live = 'I2V-01-live'
    T2V_01 = 'T2V-01'


class SubjectReferenceItem(BaseModel):
    image: Optional[str] = Field(
        None, description='URL or base64 encoding of the subject reference image.'
    )
    mask: Optional[str] = Field(
        None,
        description='URL or base64 encoding of the mask for the subject reference image.',
    )


class MinimaxVideoGenerationRequest(BaseModel):
    callback_url: Optional[str] = Field(
        None,
        description='Optional. URL to receive real-time status updates about the video generation task.',
    )
    first_frame_image: Optional[str] = Field(
        None,
        description='URL or base64 encoding of the first frame image. Required when model is I2V-01, I2V-01-Director, or I2V-01-live.',
    )
    model: Model = Field(
        ...,
        description='Required. ID of model. Options: T2V-01-Director, I2V-01-Director, S2V-01, I2V-01, I2V-01-live, T2V-01',
    )
    prompt: Optional[str] = Field(
        None,
        description='Description of the video. Should be less than 2000 characters. Supports camera movement instructions in [brackets].',
        max_length=2000,
    )
    prompt_optimizer: Optional[bool] = Field(
        True,
        description='If true (default), the model will automatically optimize the prompt. Set to false for more precise control.',
    )
    subject_reference: Optional[List[SubjectReferenceItem]] = Field(
        None,
        description='Only available when model is S2V-01. The model will generate a video based on the subject uploaded through this parameter.',
    )


class MinimaxVideoGenerationResponse(BaseModel):
    base_resp: MinimaxBaseResponse
    task_id: str = Field(
        ..., description='The task ID for the asynchronous video generation task.'
    )


class Truncation(str, Enum):
    disabled = 'disabled'
    auto = 'auto'


class ModelResponseProperties(BaseModel):
    instructions: Optional[str] = Field(
        None, description='Instructions for the model on how to generate the response'
    )
    max_output_tokens: Optional[int] = Field(
        None, description='Maximum number of tokens to generate'
    )
    model: Optional[str] = Field(
        None, description='The model used to generate the response'
    )
    temperature: Optional[float] = Field(
        1, description='Controls randomness in the response', ge=0.0, le=2.0
    )
    top_p: Optional[float] = Field(
        1,
        description='Controls diversity of the response via nucleus sampling',
        ge=0.0,
        le=1.0,
    )
    truncation: Optional[Truncation] = Field(
        'disabled', description='How to handle truncation of the response'
    )


class Moderation(str, Enum):
    low = 'low'
    auto = 'auto'


class OutputFormat1(str, Enum):
    png = 'png'
    webp = 'webp'
    jpeg = 'jpeg'


class OpenAIImageEditRequest(BaseModel):
    background: Optional[str] = Field(
        None, description='Background transparency', examples=['opaque']
    )
    model: str = Field(
        ..., description='The model to use for image editing', examples=['gpt-image-1']
    )
    moderation: Optional[Moderation] = Field(
        None, description='Content moderation setting', examples=['auto']
    )
    n: Optional[int] = Field(
        None, description='The number of images to generate', examples=[1]
    )
    output_compression: Optional[int] = Field(
        None, description='Compression level for JPEG or WebP (0-100)', examples=[100]
    )
    output_format: Optional[OutputFormat1] = Field(
        None, description='Format of the output image', examples=['png']
    )
    prompt: str = Field(
        ...,
        description='A text description of the desired edit',
        examples=['Give the rocketship rainbow coloring'],
    )
    quality: Optional[str] = Field(
        None, description='The quality of the edited image', examples=['low']
    )
    size: Optional[str] = Field(
        None, description='Size of the output image', examples=['1024x1024']
    )
    user: Optional[str] = Field(
        None,
        description='A unique identifier for end-user monitoring',
        examples=['user-1234'],
    )


class Background(str, Enum):
    transparent = 'transparent'
    opaque = 'opaque'


class Quality(str, Enum):
    low = 'low'
    medium = 'medium'
    high = 'high'
    standard = 'standard'
    hd = 'hd'


class ResponseFormat(str, Enum):
    url = 'url'
    b64_json = 'b64_json'


class Style(str, Enum):
    vivid = 'vivid'
    natural = 'natural'


class OpenAIImageGenerationRequest(BaseModel):
    background: Optional[Background] = Field(
        None, description='Background transparency', examples=['opaque']
    )
    model: Optional[str] = Field(
        None, description='The model to use for image generation', examples=['dall-e-3']
    )
    moderation: Optional[Moderation] = Field(
        None, description='Content moderation setting', examples=['auto']
    )
    n: Optional[int] = Field(
        None,
        description='The number of images to generate (1-10). Only 1 supported for dall-e-3.',
        examples=[1],
    )
    output_compression: Optional[int] = Field(
        None, description='Compression level for JPEG or WebP (0-100)', examples=[100]
    )
    output_format: Optional[OutputFormat1] = Field(
        None, description='Format of the output image', examples=['png']
    )
    prompt: str = Field(
        ...,
        description='A text description of the desired image',
        examples=['Draw a rocket in front of a blackhole in deep space'],
    )
    quality: Optional[Quality] = Field(
        None, description='The quality of the generated image', examples=['high']
    )
    response_format: Optional[ResponseFormat] = Field(
        None, description='Response format of image data', examples=['b64_json']
    )
    size: Optional[str] = Field(
        None,
        description='Size of the image (e.g., 1024x1024, 1536x1024, auto)',
        examples=['1024x1536'],
    )
    style: Optional[Style] = Field(
        None, description='Style of the image (only for dall-e-3)', examples=['vivid']
    )
    user: Optional[str] = Field(
        None,
        description='A unique identifier for end-user monitoring',
        examples=['user-1234'],
    )


class Datum2(BaseModel):
    b64_json: Optional[str] = Field(None, description='Base64 encoded image data')
    revised_prompt: Optional[str] = Field(None, description='Revised prompt')
    url: Optional[str] = Field(None, description='URL of the image')


class InputTokensDetails(BaseModel):
    image_tokens: Optional[int] = None
    text_tokens: Optional[int] = None


class Usage(BaseModel):
    input_tokens: Optional[int] = None
    input_tokens_details: Optional[InputTokensDetails] = None
    output_tokens: Optional[int] = None
    total_tokens: Optional[int] = None


class OpenAIImageGenerationResponse(BaseModel):
    data: Optional[List[Datum2]] = None
    usage: Optional[Usage] = None


class OpenAIModels(str, Enum):
    gpt_4 = 'gpt-4'
    gpt_4_0314 = 'gpt-4-0314'
    gpt_4_0613 = 'gpt-4-0613'
    gpt_4_32k = 'gpt-4-32k'
    gpt_4_32k_0314 = 'gpt-4-32k-0314'
    gpt_4_32k_0613 = 'gpt-4-32k-0613'
    gpt_4_0125_preview = 'gpt-4-0125-preview'
    gpt_4_turbo = 'gpt-4-turbo'
    gpt_4_turbo_2024_04_09 = 'gpt-4-turbo-2024-04-09'
    gpt_4_turbo_preview = 'gpt-4-turbo-preview'
    gpt_4_1106_preview = 'gpt-4-1106-preview'
    gpt_4_vision_preview = 'gpt-4-vision-preview'
    gpt_3_5_turbo = 'gpt-3.5-turbo'
    gpt_3_5_turbo_16k = 'gpt-3.5-turbo-16k'
    gpt_3_5_turbo_0301 = 'gpt-3.5-turbo-0301'
    gpt_3_5_turbo_0613 = 'gpt-3.5-turbo-0613'
    gpt_3_5_turbo_1106 = 'gpt-3.5-turbo-1106'
    gpt_3_5_turbo_0125 = 'gpt-3.5-turbo-0125'
    gpt_3_5_turbo_16k_0613 = 'gpt-3.5-turbo-16k-0613'
    gpt_4_1 = 'gpt-4.1'
    gpt_4_1_mini = 'gpt-4.1-mini'
    gpt_4_1_nano = 'gpt-4.1-nano'
    gpt_4_1_2025_04_14 = 'gpt-4.1-2025-04-14'
    gpt_4_1_mini_2025_04_14 = 'gpt-4.1-mini-2025-04-14'
    gpt_4_1_nano_2025_04_14 = 'gpt-4.1-nano-2025-04-14'
    o1 = 'o1'
    o1_mini = 'o1-mini'
    o1_preview = 'o1-preview'
    o1_pro = 'o1-pro'
    o1_2024_12_17 = 'o1-2024-12-17'
    o1_preview_2024_09_12 = 'o1-preview-2024-09-12'
    o1_mini_2024_09_12 = 'o1-mini-2024-09-12'
    o1_pro_2025_03_19 = 'o1-pro-2025-03-19'
    o3 = 'o3'
    o3_mini = 'o3-mini'
    o3_2025_04_16 = 'o3-2025-04-16'
    o3_mini_2025_01_31 = 'o3-mini-2025-01-31'
    o4_mini = 'o4-mini'
    o4_mini_2025_04_16 = 'o4-mini-2025-04-16'
    gpt_4o = 'gpt-4o'
    gpt_4o_mini = 'gpt-4o-mini'
    gpt_4o_2024_11_20 = 'gpt-4o-2024-11-20'
    gpt_4o_2024_08_06 = 'gpt-4o-2024-08-06'
    gpt_4o_2024_05_13 = 'gpt-4o-2024-05-13'
    gpt_4o_mini_2024_07_18 = 'gpt-4o-mini-2024-07-18'
    gpt_4o_audio_preview = 'gpt-4o-audio-preview'
    gpt_4o_audio_preview_2024_10_01 = 'gpt-4o-audio-preview-2024-10-01'
    gpt_4o_audio_preview_2024_12_17 = 'gpt-4o-audio-preview-2024-12-17'
    gpt_4o_mini_audio_preview = 'gpt-4o-mini-audio-preview'
    gpt_4o_mini_audio_preview_2024_12_17 = 'gpt-4o-mini-audio-preview-2024-12-17'
    gpt_4o_search_preview = 'gpt-4o-search-preview'
    gpt_4o_mini_search_preview = 'gpt-4o-mini-search-preview'
    gpt_4o_search_preview_2025_03_11 = 'gpt-4o-search-preview-2025-03-11'
    gpt_4o_mini_search_preview_2025_03_11 = 'gpt-4o-mini-search-preview-2025-03-11'
    computer_use_preview = 'computer-use-preview'
    computer_use_preview_2025_03_11 = 'computer-use-preview-2025-03-11'
    chatgpt_4o_latest = 'chatgpt-4o-latest'


class Reason(str, Enum):
    max_output_tokens = 'max_output_tokens'
    content_filter = 'content_filter'


class IncompleteDetails(BaseModel):
    reason: Optional[Reason] = Field(
        None, description='The reason why the response is incomplete.'
    )


class Object(str, Enum):
    response = 'response'


class Status6(str, Enum):
    completed = 'completed'
    failed = 'failed'
    in_progress = 'in_progress'
    incomplete = 'incomplete'


class Type13(str, Enum):
    output_audio = 'output_audio'


class OutputAudioContent(BaseModel):
    data: str = Field(..., description='Base64-encoded audio data')
    transcript: str = Field(..., description='Transcript of the audio')
    type: Type13 = Field(..., description='The type of output content')


class Role4(str, Enum):
    assistant = 'assistant'


class Type14(str, Enum):
    message = 'message'


class Type15(str, Enum):
    output_text = 'output_text'


class OutputTextContent(BaseModel):
    text: str = Field(..., description='The text content')
    type: Type15 = Field(..., description='The type of output content')


class AspectRatio1(RootModel[float]):
    root: float = Field(
        ...,
        description='Aspect ratio (width / height)',
        ge=0.4,
        le=2.5,
        title='Aspectratio',
    )


class IngredientsMode(str, Enum):
    creative = 'creative'
    precise = 'precise'


class PikaBodyGenerate22C2vGenerate22PikascenesPost(BaseModel):
    aspectRatio: Optional[AspectRatio1] = Field(
        None, description='Aspect ratio (width / height)', title='Aspectratio'
    )
    duration: Optional[int] = Field(5, title='Duration')
    images: Optional[List[StrictBytes]] = Field(None, title='Images')
    ingredientsMode: IngredientsMode = Field(..., title='Ingredientsmode')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    promptText: Optional[str] = Field(None, title='Prompttext')
    resolution: Optional[str] = Field('1080p', title='Resolution')
    seed: Optional[int] = Field(None, title='Seed')


class PikaBodyGeneratePikadditionsGeneratePikadditionsPost(BaseModel):
    image: Optional[StrictBytes] = Field(None, title='Image')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    promptText: Optional[str] = Field(None, title='Prompttext')
    seed: Optional[int] = Field(None, title='Seed')
    video: Optional[StrictBytes] = Field(None, title='Video')


class PikaBodyGeneratePikaswapsGeneratePikaswapsPost(BaseModel):
    image: Optional[StrictBytes] = Field(None, title='Image')
    modifyRegionMask: Optional[StrictBytes] = Field(
        None,
        description='A mask image that specifies the region to modify, where the mask is white and the background is black',
        title='Modifyregionmask',
    )
    modifyRegionRoi: Optional[str] = Field(
        None,
        description='Plaintext description of the object / region to modify',
        title='Modifyregionroi',
    )
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    promptText: Optional[str] = Field(None, title='Prompttext')
    seed: Optional[int] = Field(None, title='Seed')
    video: Optional[StrictBytes] = Field(None, title='Video')


class PikaDurationEnum(int, Enum):
    integer_5 = 5
    integer_10 = 10


class PikaGenerateResponse(BaseModel):
    video_id: str = Field(..., title='Video Id')


class PikaResolutionEnum(str, Enum):
    field_1080p = '1080p'
    field_720p = '720p'


class PikaStatusEnum(str, Enum):
    queued = 'queued'
    started = 'started'
    finished = 'finished'


class PikaValidationError(BaseModel):
    loc: List[Union[str, int]] = Field(..., title='Location')
    msg: str = Field(..., title='Message')
    type: str = Field(..., title='Error Type')


class PikaVideoResponse(BaseModel):
    id: str = Field(..., title='Id')
    progress: Optional[int] = Field(None, title='Progress')
    status: PikaStatusEnum
    url: Optional[str] = Field(None, title='Url')


class Pikaffect(str, Enum):
    Cake_ify = 'Cake-ify'
    Crumble = 'Crumble'
    Crush = 'Crush'
    Decapitate = 'Decapitate'
    Deflate = 'Deflate'
    Dissolve = 'Dissolve'
    Explode = 'Explode'
    Eye_pop = 'Eye-pop'
    Inflate = 'Inflate'
    Levitate = 'Levitate'
    Melt = 'Melt'
    Peel = 'Peel'
    Poke = 'Poke'
    Squish = 'Squish'
    Ta_da = 'Ta-da'
    Tear = 'Tear'


class Resp(BaseModel):
    img_id: Optional[int] = None


class PixverseImageUploadResponse(BaseModel):
    ErrCode: Optional[int] = None
    ErrMsg: Optional[str] = None
    Resp_1: Optional[Resp] = Field(None, alias='Resp')


class Duration(int, Enum):
    integer_5 = 5
    integer_8 = 8


class Model1(str, Enum):
    v3_5 = 'v3.5'


class MotionMode(str, Enum):
    normal = 'normal'
    fast = 'fast'


class Quality1(str, Enum):
    field_360p = '360p'
    field_540p = '540p'
    field_720p = '720p'
    field_1080p = '1080p'


class Style1(str, Enum):
    anime = 'anime'
    field_3d_animation = '3d_animation'
    clay = 'clay'
    comic = 'comic'
    cyberpunk = 'cyberpunk'


class PixverseImageVideoRequest(BaseModel):
    duration: Duration
    img_id: int
    model: Model1
    motion_mode: Optional[MotionMode] = None
    prompt: str
    quality: Quality1
    seed: Optional[int] = None
    style: Optional[Style1] = None
    template_id: Optional[int] = None
    water_mark: Optional[bool] = None


class AspectRatio2(str, Enum):
    field_16_9 = '16:9'
    field_4_3 = '4:3'
    field_1_1 = '1:1'
    field_3_4 = '3:4'
    field_9_16 = '9:16'


class PixverseTextVideoRequest(BaseModel):
    aspect_ratio: AspectRatio2
    duration: Duration
    model: Model1
    motion_mode: Optional[MotionMode] = None
    negative_prompt: Optional[str] = None
    prompt: str
    quality: Quality1
    seed: Optional[int] = None
    style: Optional[Style1] = None
    template_id: Optional[int] = None
    water_mark: Optional[bool] = None


class PixverseTransitionVideoRequest(BaseModel):
    duration: Duration
    first_frame_img: int
    last_frame_img: int
    model: Model1
    motion_mode: MotionMode
    prompt: str
    quality: Quality1
    seed: int
    style: Optional[Style1] = None
    template_id: Optional[int] = None
    water_mark: Optional[bool] = None


class Resp1(BaseModel):
    video_id: Optional[int] = None


class PixverseVideoResponse(BaseModel):
    ErrCode: Optional[int] = None
    ErrMsg: Optional[str] = None
    Resp: Optional[Resp1] = None


class Status7(int, Enum):
    integer_1 = 1
    integer_5 = 5
    integer_6 = 6
    integer_7 = 7
    integer_8 = 8


class Resp2(BaseModel):
    create_time: Optional[str] = None
    id: Optional[int] = None
    modify_time: Optional[str] = None
    negative_prompt: Optional[str] = None
    outputHeight: Optional[int] = None
    outputWidth: Optional[int] = None
    prompt: Optional[str] = None
    resolution_ratio: Optional[int] = None
    seed: Optional[int] = None
    size: Optional[int] = None
    status: Optional[Status7] = Field(
        None,
        description='Video generation status codes:\n* 1 - Generation successful\n* 5 - Generating\n* 6 - Deleted\n* 7 - Contents moderation failed\n* 8 - Generation failed\n',
    )
    style: Optional[str] = None
    url: Optional[str] = None


class PixverseVideoResultResponse(BaseModel):
    ErrCode: Optional[int] = None
    ErrMsg: Optional[str] = None
    Resp: Optional[Resp2] = None


class RgbItem(RootModel[int]):
    root: int = Field(..., ge=0, le=255)


class RGBColor(BaseModel):
    rgb: List[RgbItem] = Field(..., max_length=3, min_length=3)


class GenerateSummary(str, Enum):
    auto = 'auto'
    concise = 'concise'
    detailed = 'detailed'


class Summary(str, Enum):
    auto = 'auto'
    concise = 'concise'
    detailed = 'detailed'


class ReasoningEffort(str, Enum):
    low = 'low'
    medium = 'medium'
    high = 'high'


class Status8(str, Enum):
    in_progress = 'in_progress'
    completed = 'completed'
    incomplete = 'incomplete'


class Type16(str, Enum):
    summary_text = 'summary_text'


class SummaryItem(BaseModel):
    text: str = Field(
        ...,
        description='A short summary of the reasoning used by the model when generating\nthe response.\n',
    )
    type: Type16 = Field(
        ..., description='The type of the object. Always `summary_text`.\n'
    )


class Type17(str, Enum):
    reasoning = 'reasoning'


class ReasoningItem(BaseModel):
    id: str = Field(
        ..., description='The unique identifier of the reasoning content.\n'
    )
    status: Optional[Status8] = Field(
        None,
        description='The status of the item. One of `in_progress`, `completed`, or\n`incomplete`. Populated when items are returned via API.\n',
    )
    summary: List[SummaryItem] = Field(..., description='Reasoning text contents.\n')
    type: Type17 = Field(
        ..., description='The type of the object. Always `reasoning`.\n'
    )


class Controls(BaseModel):
    artistic_level: Optional[int] = Field(
        None,
        description='Defines artistic tone of your image. At a simple level, the person looks straight at the camera in a static and clean style. Dynamic and eccentric levels introduce movement and creativity.',
        ge=0,
        le=5,
    )
    background_color: Optional[RGBColor] = None
    colors: Optional[List[RGBColor]] = Field(
        None, description='An array of preferable colors'
    )
    no_text: Optional[bool] = Field(None, description='Do not embed text layouts')


class RecraftImageGenerationRequest(BaseModel):
    controls: Optional[Controls] = Field(
        None, description='The controls for the generated image'
    )
    model: str = Field(
        ..., description='The model to use for generation (e.g., "recraftv3")'
    )
    n: int = Field(..., description='The number of images to generate', ge=1, le=4)
    prompt: str = Field(
        ..., description='The text prompt describing the image to generate'
    )
    size: str = Field(
        ..., description='The size of the generated image (e.g., "1024x1024")'
    )
    style: Optional[str] = Field(
        None,
        description='The style to apply to the generated image (e.g., "digital_illustration")',
    )
    style_id: Optional[str] = Field(
        None,
        description='The style ID to apply to the generated image (e.g., "123e4567-e89b-12d3-a456-426614174000"). If style_id is provided, style should not be provided.',
    )


class Datum3(BaseModel):
    image_id: Optional[str] = Field(
        None, description='Unique identifier for the generated image'
    )
    url: Optional[str] = Field(None, description='URL to access the generated image')


class RecraftImageGenerationResponse(BaseModel):
    created: int = Field(
        ..., description='Unix timestamp when the generation was created'
    )
    credits: int = Field(..., description='Number of credits used for the generation')
    data: List[Datum3] = Field(..., description='Array of generated image information')


class RenderingSpeed(str, Enum):
    BALANCED = 'BALANCED'
    TURBO = 'TURBO'
    QUALITY = 'QUALITY'


class ResponseErrorCode(str, Enum):
    server_error = 'server_error'
    rate_limit_exceeded = 'rate_limit_exceeded'
    invalid_prompt = 'invalid_prompt'
    vector_store_timeout = 'vector_store_timeout'
    invalid_image = 'invalid_image'
    invalid_image_format = 'invalid_image_format'
    invalid_base64_image = 'invalid_base64_image'
    invalid_image_url = 'invalid_image_url'
    image_too_large = 'image_too_large'
    image_too_small = 'image_too_small'
    image_parse_error = 'image_parse_error'
    image_content_policy_violation = 'image_content_policy_violation'
    invalid_image_mode = 'invalid_image_mode'
    image_file_too_large = 'image_file_too_large'
    unsupported_image_media_type = 'unsupported_image_media_type'
    empty_image_file = 'empty_image_file'
    failed_to_download_image = 'failed_to_download_image'
    image_file_not_found = 'image_file_not_found'


class Type18(str, Enum):
    json_object = 'json_object'


class ResponseFormatJsonObject(BaseModel):
    type: Type18 = Field(
        ...,
        description='The type of response format being defined. Always `json_object`.',
    )


class ResponseFormatJsonSchemaSchema(BaseModel):
    pass
    model_config = ConfigDict(
        extra='allow',
    )


class Type19(str, Enum):
    text = 'text'


class ResponseFormatText(BaseModel):
    type: Type19 = Field(
        ..., description='The type of response format being defined. Always `text`.'
    )


class Truncation1(str, Enum):
    auto = 'auto'
    disabled = 'disabled'


class InputTokensDetails1(BaseModel):
    cached_tokens: int = Field(
        ...,
        description='The number of tokens that were retrieved from the cache. \n[More on prompt caching](/docs/guides/prompt-caching).\n',
    )


class OutputTokensDetails(BaseModel):
    reasoning_tokens: int = Field(..., description='The number of reasoning tokens.')


class ResponseUsage(BaseModel):
    input_tokens: int = Field(..., description='The number of input tokens.')
    input_tokens_details: InputTokensDetails1 = Field(
        ..., description='A detailed breakdown of the input tokens.'
    )
    output_tokens: int = Field(..., description='The number of output tokens.')
    output_tokens_details: OutputTokensDetails = Field(
        ..., description='A detailed breakdown of the output tokens.'
    )
    total_tokens: int = Field(..., description='The total number of tokens used.')


class Rodin3DCheckStatusRequest(BaseModel):
    subscription_key: str = Field(
        ..., description='subscription from generate endpoint'
    )


class Rodin3DCheckStatusResponse(BaseModel):
    pass


class Rodin3DDownloadRequest(BaseModel):
    task_uuid: str = Field(..., description='Task UUID')


class RodinGenerateJobsData(BaseModel):
    subscription_key: Optional[str] = Field(None, description='Subscription Key.')
    uuids: Optional[List[str]] = Field(None, description='subjobs uuid.')


class RodinMaterialType(str, Enum):
    PBR = 'PBR'
    Shaded = 'Shaded'


class RodinMeshModeType(str, Enum):
    Quad = 'Quad'
    Raw = 'Raw'


class RodinQualityType(str, Enum):
    extra_low = 'extra-low'
    low = 'low'
    medium = 'medium'
    high = 'high'


class RodinResourceItem(BaseModel):
    name: Optional[str] = Field(None, description='File name')
    url: Optional[str] = Field(None, description='Download url')


class RodinTierType(str, Enum):
    Regular = 'Regular'
    Sketch = 'Sketch'
    Detail = 'Detail'
    Smooth = 'Smooth'


class RunwayAspectRatioEnum(str, Enum):
    field_1280_720 = '1280:720'
    field_720_1280 = '720:1280'
    field_1104_832 = '1104:832'
    field_832_1104 = '832:1104'
    field_960_960 = '960:960'
    field_1584_672 = '1584:672'
    field_1280_768 = '1280:768'
    field_768_1280 = '768:1280'


class RunwayDurationEnum(int, Enum):
    integer_5 = 5
    integer_10 = 10


class RunwayImageToVideoResponse(BaseModel):
    id: Optional[str] = Field(None, description='Task ID')


class RunwayModelEnum(str, Enum):
    gen4_turbo = 'gen4_turbo'
    gen3a_turbo = 'gen3a_turbo'


class Position(str, Enum):
    first = 'first'
    last = 'last'


class RunwayPromptImageDetailedObject(BaseModel):
    position: Position = Field(
        ...,
        description="The position of the image in the output video. 'last' is currently supported for gen3a_turbo only.",
    )
    uri: str = Field(
        ..., description='A HTTPS URL or data URI containing an encoded image.'
    )


class RunwayPromptImageObject(
    RootModel[Union[str, List[RunwayPromptImageDetailedObject]]]
):
    root: Union[str, List[RunwayPromptImageDetailedObject]] = Field(
        ...,
        description='Image(s) to use for the video generation. Can be a single URI or an array of image objects with positions.',
    )


class RunwayTaskStatusEnum(str, Enum):
    SUCCEEDED = 'SUCCEEDED'
    RUNNING = 'RUNNING'
    FAILED = 'FAILED'
    PENDING = 'PENDING'
    CANCELLED = 'CANCELLED'
    THROTTLED = 'THROTTLED'


class RunwayTaskStatusResponse(BaseModel):
    createdAt: datetime = Field(..., description='Task creation timestamp')
    id: str = Field(..., description='Task ID')
    output: Optional[List[str]] = Field(None, description='Array of output video URLs')
    progress: Optional[float] = Field(
        None,
        description='Float value between 0 and 1 representing the progress of the task. Only available if status is RUNNING.',
        ge=0.0,
        le=1.0,
    )
    status: RunwayTaskStatusEnum


class RunwayTextToImageAspectRatioEnum(str, Enum):
    field_1920_1080 = '1920:1080'
    field_1080_1920 = '1080:1920'
    field_1024_1024 = '1024:1024'
    field_1360_768 = '1360:768'
    field_1080_1080 = '1080:1080'
    field_1168_880 = '1168:880'
    field_1440_1080 = '1440:1080'
    field_1080_1440 = '1080:1440'
    field_1808_768 = '1808:768'
    field_2112_912 = '2112:912'

class Model4(str, Enum):
    gen4_image = 'gen4_image'


class ReferenceImage(BaseModel):
    uri: Optional[str] = Field(
        None, description='A HTTPS URL or data URI containing an encoded image'
    )


class RunwayTextToImageRequest(BaseModel):
    model: Model4 = Field(..., description='Model to use for generation')
    promptText: str = Field(
        ..., description='Text prompt for the image generation', max_length=1000
    )
    ratio: RunwayTextToImageAspectRatioEnum
    referenceImages: Optional[List[ReferenceImage]] = Field(
        None, description='Array of reference images to guide the generation'
    )


class RunwayTextToImageResponse(BaseModel):
    id: Optional[str] = Field(None, description='Task ID')


class StabilityError(BaseModel):
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[[{'some-field': 'is required'}]],
        min_length=1,
    )
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new) you file, as it will greatly assist us in diagnosing the root cause of the problem.\n',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )


class Status9(str, Enum):
    in_progress = 'in-progress'


class StabilityGetResultResponse202(BaseModel):
    id: Optional[str] = Field(
        None, description='The ID of the generation result.', examples=[1234567890]
    )
    status: Optional[Status9] = None


class Type20(str, Enum):
    json_schema = 'json_schema'


class TextResponseFormatJsonSchema(BaseModel):
    description: Optional[str] = Field(
        None,
        description='A description of what the response format is for, used by the model to\ndetermine how to respond in the format.\n',
    )
    name: str = Field(
        ...,
        description='The name of the response format. Must be a-z, A-Z, 0-9, or contain\nunderscores and dashes, with a maximum length of 64.\n',
    )
    schema_: ResponseFormatJsonSchemaSchema = Field(..., alias='schema')
    strict: Optional[bool] = Field(
        False,
        description='Whether to enable strict schema adherence when generating the output.\nIf set to true, the model will always follow the exact schema defined\nin the `schema` field. Only a subset of JSON Schema is supported when\n`strict` is `true`. To learn more, read the [Structured Outputs\nguide](/docs/guides/structured-outputs).\n',
    )
    type: Type20 = Field(
        ...,
        description='The type of response format being defined. Always `json_schema`.',
    )


class Type21(str, Enum):
    function = 'function'


class ToolChoiceFunction(BaseModel):
    name: str = Field(..., description='The name of the function to call.')
    type: Type21 = Field(
        ..., description='For function calling, the type is always `function`.'
    )


class ToolChoiceOptions(str, Enum):
    none = 'none'
    auto = 'auto'
    required = 'required'


class Type22(str, Enum):
    file_search = 'file_search'
    web_search_preview = 'web_search_preview'
    computer_use_preview = 'computer_use_preview'
    web_search_preview_2025_03_11 = 'web_search_preview_2025_03_11'


class ToolChoiceTypes(BaseModel):
    type: Type22 = Field(
        ...,
        description='The type of hosted tool the model should to use. Learn more about\n[built-in tools](/docs/guides/tools).\n\nAllowed values are:\n- `file_search`\n- `web_search_preview`\n- `computer_use_preview`\n',
    )


class TripoAnimation(str, Enum):
    preset_idle = 'preset:idle'
    preset_walk = 'preset:walk'
    preset_climb = 'preset:climb'
    preset_jump = 'preset:jump'
    preset_run = 'preset:run'
    preset_slash = 'preset:slash'
    preset_shoot = 'preset:shoot'
    preset_hurt = 'preset:hurt'
    preset_fall = 'preset:fall'
    preset_turn = 'preset:turn'


class TripoBalance(BaseModel):
    balance: float
    frozen: float


class TripoConvertFormat(str, Enum):
    GLTF = 'GLTF'
    USDZ = 'USDZ'
    FBX = 'FBX'
    OBJ = 'OBJ'
    STL = 'STL'
    field_3MF = '3MF'


class Code(int, Enum):
    integer_1001 = 1001
    integer_2000 = 2000
    integer_2001 = 2001
    integer_2002 = 2002
    integer_2003 = 2003
    integer_2004 = 2004
    integer_2006 = 2006
    integer_2007 = 2007
    integer_2008 = 2008
    integer_2010 = 2010


class TripoErrorResponse(BaseModel):
    code: Code
    message: str
    suggestion: str


class TripoImageToModel(str, Enum):
    image_to_model = 'image_to_model'


class TripoModelStyle(str, Enum):
    person_person2cartoon = 'person:person2cartoon'
    animal_venom = 'animal:venom'
    object_clay = 'object:clay'
    object_steampunk = 'object:steampunk'
    object_christmas = 'object:christmas'
    object_barbie = 'object:barbie'
    gold = 'gold'
    ancient_bronze = 'ancient_bronze'


class TripoModelVersion(str, Enum):
    V2_5 = 'v2.5-20250123'
    V2_0 = 'v2.0-20240919'
    V1_4 = 'v1.4-20240625'


class TripoMultiviewMode(str, Enum):
    LEFT = 'LEFT'
    RIGHT = 'RIGHT'


class TripoMultiviewToModel(str, Enum):
    multiview_to_model = 'multiview_to_model'


class TripoOrientation(str, Enum):
    align_image = 'align_image'
    default = 'default'


class TripoResponseSuccessCode(RootModel[int]):
    root: int = Field(
        ...,
        description='Standard success code for Tripo API responses. Typically 0 for success.',
        examples=[0],
    )


class TripoSpec(str, Enum):
    mixamo = 'mixamo'
    tripo = 'tripo'


class TripoStandardFormat(str, Enum):
    glb = 'glb'
    fbx = 'fbx'


class TripoStylizeOptions(str, Enum):
    lego = 'lego'
    voxel = 'voxel'
    voronoi = 'voronoi'
    minecraft = 'minecraft'


class Code1(int, Enum):
    integer_0 = 0


class Data8(BaseModel):
    task_id: str = Field(..., description='used for getTask')


class TripoSuccessTask(BaseModel):
    code: Code1
    data: Data8


class Topology(str, Enum):
    bip = 'bip'
    quad = 'quad'


class Output(BaseModel):
    base_model: Optional[str] = None
    model: Optional[str] = None
    pbr_model: Optional[str] = None
    rendered_image: Optional[str] = None
    riggable: Optional[bool] = None
    topology: Optional[Topology] = None


class Status10(str, Enum):
    queued = 'queued'
    running = 'running'
    success = 'success'
    failed = 'failed'
    cancelled = 'cancelled'
    unknown = 'unknown'
    banned = 'banned'
    expired = 'expired'


class TripoTask(BaseModel):
    create_time: int
    input: Dict[str, Any]
    output: Output
    progress: int = Field(..., ge=0, le=100)
    status: Status10
    task_id: str
    type: str


class TripoTextToModel(str, Enum):
    text_to_model = 'text_to_model'


class TripoTextureAlignment(str, Enum):
    original_image = 'original_image'
    geometry = 'geometry'


class TripoTextureFormat(str, Enum):
    BMP = 'BMP'
    DPX = 'DPX'
    HDR = 'HDR'
    JPEG = 'JPEG'
    OPEN_EXR = 'OPEN_EXR'
    PNG = 'PNG'
    TARGA = 'TARGA'
    TIFF = 'TIFF'
    WEBP = 'WEBP'


class TripoTextureQuality(str, Enum):
    standard = 'standard'
    detailed = 'detailed'


class TripoTopology(str, Enum):
    bip = 'bip'
    quad = 'quad'


class TripoTypeAnimatePrerigcheck(str, Enum):
    animate_prerigcheck = 'animate_prerigcheck'


class TripoTypeAnimateRetarget(str, Enum):
    animate_retarget = 'animate_retarget'


class TripoTypeAnimateRig(str, Enum):
    animate_rig = 'animate_rig'


class TripoTypeConvertModel(str, Enum):
    convert_model = 'convert_model'


class TripoTypeRefineModel(str, Enum):
    refine_model = 'refine_model'


class TripoTypeStylizeModel(str, Enum):
    stylize_model = 'stylize_model'


class TripoTypeTextureModel(str, Enum):
    texture_model = 'texture_model'


class Veo2GenVidPollRequest(BaseModel):
    operationName: str = Field(
        ...,
        description='Full operation name (from predict response)',
        examples=[
            'projects/PROJECT_ID/locations/us-central1/publishers/google/models/MODEL_ID/operations/OPERATION_ID'
        ],
    )


class Error(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')


class Video(BaseModel):
    bytesBase64Encoded: Optional[str] = Field(
        None, description='Base64-encoded video content'
    )
    gcsUri: Optional[str] = Field(None, description='Cloud Storage URI of the video')
    mimeType: Optional[str] = Field(None, description='Video MIME type')


class Response(BaseModel):
    field_type: Optional[str] = Field(
        None,
        alias='@type',
        examples=[
            'type.googleapis.com/cloud.ai.large_models.vision.GenerateVideoResponse'
        ],
    )
    raiMediaFilteredCount: Optional[int] = Field(
        None, description='Count of media filtered by responsible AI policies'
    )
    raiMediaFilteredReasons: Optional[List[str]] = Field(
        None, description='Reasons why media was filtered by responsible AI policies'
    )
    videos: Optional[List[Video]] = None


class Veo2GenVidPollResponse(BaseModel):
    done: Optional[bool] = None
    error: Optional[Error] = Field(
        None, description='Error details if operation failed'
    )
    name: Optional[str] = None
    response: Optional[Response] = Field(
        None, description='The actual prediction response if done is true'
    )


class Image(BaseModel):
    bytesBase64Encoded: str
    gcsUri: Optional[str] = None
    mimeType: Optional[str] = None


class Image1(BaseModel):
    bytesBase64Encoded: Optional[str] = None
    gcsUri: str
    mimeType: Optional[str] = None


class Instance(BaseModel):
    image: Optional[Union[Image, Image1]] = Field(
        None, description='Optional image to guide video generation'
    )
    prompt: str = Field(..., description='Text description of the video')


class PersonGeneration1(str, Enum):
    ALLOW = 'ALLOW'
    BLOCK = 'BLOCK'


class Parameters(BaseModel):
    aspectRatio: Optional[str] = Field(None, examples=['16:9'])
    durationSeconds: Optional[int] = None
    enhancePrompt: Optional[bool] = None
    negativePrompt: Optional[str] = None
    personGeneration: Optional[PersonGeneration1] = None
    sampleCount: Optional[int] = None
    seed: Optional[int] = None
    storageUri: Optional[str] = Field(
        None, description='Optional Cloud Storage URI to upload the video'
    )


class Veo2GenVidRequest(BaseModel):
    instances: Optional[List[Instance]] = None
    parameters: Optional[Parameters] = None


class Veo2GenVidResponse(BaseModel):
    name: str = Field(
        ...,
        description='Operation resource name',
        examples=[
            'projects/PROJECT_ID/locations/us-central1/publishers/google/models/MODEL_ID/operations/a1b07c8e-7b5a-4aba-bb34-3e1ccb8afcc8'
        ],
    )


class SearchContextSize(str, Enum):
    low = 'low'
    medium = 'medium'
    high = 'high'


class Type23(str, Enum):
    web_search_preview = 'web_search_preview'
    web_search_preview_2025_03_11 = 'web_search_preview_2025_03_11'


class WebSearchPreviewTool(BaseModel):
    search_context_size: Optional[SearchContextSize] = Field(
        None,
        description='High level guidance for the amount of context window space to use for the search. One of `low`, `medium`, or `high`. `medium` is the default.',
    )
    type: Literal['WebSearchPreviewTool'] = Field(
        ...,
        description='The type of the web search tool. One of `web_search_preview` or `web_search_preview_2025_03_11`.',
    )


class Status11(str, Enum):
    in_progress = 'in_progress'
    searching = 'searching'
    completed = 'completed'
    failed = 'failed'


class Type24(str, Enum):
    web_search_call = 'web_search_call'


class WebSearchToolCall(BaseModel):
    id: str = Field(..., description='The unique ID of the web search tool call.\n')
    status: Status11 = Field(
        ..., description='The status of the web search tool call.\n'
    )
    type: Type24 = Field(
        ...,
        description='The type of the web search tool call. Always `web_search_call`.\n',
    )


class CreateModelResponseProperties(ModelResponseProperties):
    pass


class GeminiInlineData(BaseModel):
    data: Optional[str] = Field(
        None,
        description='The base64 encoding of the image, PDF, or video to include inline in the prompt. When including media inline, you must also specify the media type (mimeType) of the data. Size limit: 20MB\n',
    )
    mimeType: Optional[GeminiMimeType] = None


class GeminiPart(BaseModel):
    inlineData: Optional[GeminiInlineData] = None
    text: Optional[str] = Field(
        None,
        description='A text prompt or code snippet.',
        examples=['Write a story about a robot learning to paint'],
    )


class GeminiPromptFeedback(BaseModel):
    blockReason: Optional[str] = None
    blockReasonMessage: Optional[str] = None
    safetyRatings: Optional[List[GeminiSafetyRating]] = None


class GeminiSafetySetting(BaseModel):
    category: GeminiSafetyCategory
    threshold: GeminiSafetyThreshold


class GeminiSystemInstructionContent(BaseModel):
    parts: List[GeminiTextPart] = Field(
        ...,
        description='A list of ordered parts that make up a single message. Different parts may have different IANA MIME types. For limits on the inputs, such as the maximum number of tokens or the number of images, see the model specifications on the Google models page.\n',
    )
    role: Role1 = Field(
        ...,
        description='The identity of the entity that creates the message. The following values are supported: user: This indicates that the message is sent by a real person, typically a user-generated message. model: This indicates that the message is generated by the model. The model value is used to insert messages from the model into the conversation during multi-turn conversations. For non-multi-turn conversations, this field can be left blank or unset.\n',
        examples=['user'],
    )


class IdeogramV3EditRequest(BaseModel):
    color_palette: Optional[IdeogramColorPalette] = None
    image: Optional[StrictBytes] = Field(
        None,
        description='The image being edited (max size 10MB); only JPEG, WebP and PNG formats are supported at this time.',
    )
    magic_prompt: Optional[str] = Field(
        None,
        description='Determine if MagicPrompt should be used in generating the request or not.',
    )
    mask: Optional[StrictBytes] = Field(
        None,
        description='A black and white image of the same size as the image being edited (max size 10MB). Black regions in the mask should match up with the regions of the image that you would like to edit; only JPEG, WebP and PNG formats are supported at this time.',
    )
    num_images: Optional[int] = Field(
        None, description='The number of images to generate.'
    )
    prompt: str = Field(
        ..., description='The prompt used to describe the edited result.'
    )
    rendering_speed: RenderingSpeed
    seed: Optional[int] = Field(
        None, description='Random seed. Set for reproducible generation.'
    )
    style_codes: Optional[List[StyleCode]] = Field(
        None,
        description='A list of 8 character hexadecimal codes representing the style of the image. Cannot be used in conjunction with style_reference_images or style_type.',
    )
    style_reference_images: Optional[List[StrictBytes]] = Field(
        None,
        description='A set of images to use as style references (maximum total size 10MB across all style references). The images should be in JPEG, PNG or WebP format.',
    )


class IdeogramV3Request(BaseModel):
    aspect_ratio: Optional[str] = Field(
        None, description='Aspect ratio in format WxH', examples=['1x3']
    )
    color_palette: Optional[ColorPalette] = None
    magic_prompt: Optional[MagicPrompt2] = Field(
        None, description='Whether to enable magic prompt enhancement'
    )
    negative_prompt: Optional[str] = Field(
        None, description='Text prompt specifying what to avoid in the generation'
    )
    num_images: Optional[int] = Field(
        None, description='Number of images to generate', ge=1
    )
    prompt: str = Field(..., description='The text prompt for image generation')
    rendering_speed: RenderingSpeed
    resolution: Optional[str] = Field(
        None, description='Image resolution in format WxH', examples=['1280x800']
    )
    seed: Optional[int] = Field(
        None, description='Seed value for reproducible generation'
    )
    style_codes: Optional[List[StyleCode]] = Field(
        None, description='Array of style codes in hexadecimal format'
    )
    style_reference_images: Optional[List[str]] = Field(
        None, description='Array of reference image URLs or identifiers'
    )
    style_type: Optional[StyleType1] = Field(
        None, description='The type of style to apply'
    )


class ImagenGenerateImageResponse(BaseModel):
    predictions: Optional[List[ImagenImagePrediction]] = None


class ImagenImageGenerationParameters(BaseModel):
    addWatermark: Optional[bool] = None
    aspectRatio: Optional[AspectRatio] = None
    enhancePrompt: Optional[bool] = None
    includeRaiReason: Optional[bool] = None
    includeSafetyAttributes: Optional[bool] = None
    outputOptions: Optional[ImagenOutputOptions] = None
    personGeneration: Optional[PersonGeneration] = None
    safetySetting: Optional[SafetySetting] = None
    sampleCount: Optional[int] = Field(None, ge=1, le=4)
    seed: Optional[int] = None
    storageUri: Optional[AnyUrl] = None


class InputContent(
    RootModel[Union[InputTextContent, InputImageContent, InputFileContent]]
):
    root: Union[InputTextContent, InputImageContent, InputFileContent]


class InputMessageContentList(RootModel[List[InputContent]]):
    root: List[InputContent] = Field(
        ...,
        description='A list of one or many input items to the model, containing different content \ntypes.\n',
        title='Input item content list',
    )


class KlingCameraControl(BaseModel):
    config: Optional[KlingCameraConfig] = None
    type: Optional[KlingCameraControlType] = None


class KlingDualCharacterEffectInput(BaseModel):
    duration: KlingVideoGenDuration
    images: KlingDualCharacterImages
    mode: Optional[KlingVideoGenMode] = 'std'
    model_name: Optional[KlingCharacterEffectModelName] = 'kling-v1'


class KlingImage2VideoRequest(BaseModel):
    aspect_ratio: Optional[KlingVideoGenAspectRatio] = '16:9'
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address. Server will notify when the task status changes.',
    )
    camera_control: Optional[KlingCameraControl] = None
    cfg_scale: Optional[KlingVideoGenCfgScale] = Field(
        default_factory=lambda: KlingVideoGenCfgScale.model_validate(0.5)
    )
    duration: Optional[KlingVideoGenDuration] = '5'
    dynamic_masks: Optional[List[DynamicMask]] = Field(
        None,
        description='Dynamic Brush Configuration List (up to 6 groups). For 5-second videos, trajectory length must not exceed 77 coordinates.',
    )
    external_task_id: Optional[str] = Field(
        None,
        description='Customized Task ID. Must be unique within a single user account.',
    )
    image: Optional[str] = Field(
        None,
        description='Reference Image - URL or Base64 encoded string, cannot exceed 10MB, resolution not less than 300*300px, aspect ratio between 1:2.5 ~ 2.5:1. Base64 should not include data:image prefix.',
    )
    image_tail: Optional[str] = Field(
        None,
        description='Reference Image - End frame control. URL or Base64 encoded string, cannot exceed 10MB, resolution not less than 300*300px. Base64 should not include data:image prefix.',
    )
    mode: Optional[KlingVideoGenMode] = 'std'
    model_name: Optional[KlingVideoGenModelName] = 'kling-v2-master'
    negative_prompt: Optional[str] = Field(
        None, description='Negative text prompt', max_length=2500
    )
    prompt: Optional[str] = Field(
        None, description='Positive text prompt', max_length=2500
    )
    static_mask: Optional[str] = Field(
        None,
        description='Static Brush Application Area (Mask image created by users using the motion brush). The aspect ratio must match the input image.',
    )


class TaskResult(BaseModel):
    videos: Optional[List[KlingVideoResult]] = None


class Data(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_info: Optional[TaskInfo] = None
    task_result: Optional[TaskResult] = None
    task_status: Optional[KlingTaskStatus] = None
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingImage2VideoResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class TaskResult1(BaseModel):
    images: Optional[List[KlingImageResult]] = None


class Data1(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_result: Optional[TaskResult1] = None
    task_status: Optional[KlingTaskStatus] = None
    task_status_msg: Optional[str] = Field(None, description='Task status information')
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingImageGenerationsResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data1] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class KlingLipSyncInputObject(BaseModel):
    audio_file: Optional[str] = Field(
        None,
        description='Local Path of Audio File. Supported formats: .mp3/.wav/.m4a/.aac, maximum file size of 5MB. Base64 code.',
    )
    audio_type: Optional[KlingAudioUploadType] = None
    audio_url: Optional[str] = Field(
        None,
        description='Audio File Download URL. Supported formats: .mp3/.wav/.m4a/.aac, maximum file size of 5MB.',
    )
    mode: KlingLipSyncMode
    text: Optional[str] = Field(
        None,
        description='Text Content for Lip-Sync Video Generation. Required when mode is text2video. Maximum length is 120 characters.',
    )
    video_id: Optional[str] = Field(
        None,
        description='The ID of the video generated by Kling AI. Only supports 5-second and 10-second videos generated within the last 30 days.',
    )
    video_url: Optional[str] = Field(
        None,
        description='Get link for uploaded video. Video files support .mp4/.mov, file size does not exceed 100MB, video length between 2-10s.',
    )
    voice_id: Optional[str] = Field(
        None,
        description='Voice ID. Required when mode is text2video. The system offers a variety of voice options to choose from.',
    )
    voice_language: Optional[KlingLipSyncVoiceLanguage] = 'en'
    voice_speed: Optional[float] = Field(
        1,
        description='Speech Rate. Valid range: 0.8~2.0, accurate to one decimal place.',
        ge=0.8,
        le=2.0,
    )


class KlingLipSyncRequest(BaseModel):
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address. Server will notify when the task status changes.',
    )
    input: KlingLipSyncInputObject


class TaskResult2(BaseModel):
    videos: Optional[List[KlingVideoResult]] = None


class Data2(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_info: Optional[TaskInfo] = None
    task_result: Optional[TaskResult2] = None
    task_status: Optional[KlingTaskStatus] = None
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingLipSyncResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data2] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class KlingSingleImageEffectInput(BaseModel):
    duration: KlingSingleImageEffectDuration
    image: str = Field(
        ...,
        description='Reference Image. URL or Base64 encoded string (without data:image prefix). File size cannot exceed 10MB, resolution not less than 300*300px, aspect ratio between 1:2.5 ~ 2.5:1.',
    )
    model_name: KlingSingleImageEffectModelName


class KlingText2VideoRequest(BaseModel):
    aspect_ratio: Optional[KlingVideoGenAspectRatio] = '16:9'
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback notification address'
    )
    camera_control: Optional[KlingCameraControl] = None
    cfg_scale: Optional[KlingVideoGenCfgScale] = Field(
        default_factory=lambda: KlingVideoGenCfgScale.model_validate(0.5)
    )
    duration: Optional[KlingVideoGenDuration] = '5'
    external_task_id: Optional[str] = Field(None, description='Customized Task ID')
    mode: Optional[KlingVideoGenMode] = 'std'
    model_name: Optional[KlingTextToVideoModelName] = 'kling-v1'
    negative_prompt: Optional[str] = Field(
        None, description='Negative text prompt', max_length=2500
    )
    prompt: Optional[str] = Field(
        None, description='Positive text prompt', max_length=2500
    )


class Data4(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_info: Optional[TaskInfo] = None
    task_result: Optional[TaskResult2] = None
    task_status: Optional[KlingTaskStatus] = None
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingText2VideoResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data4] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class KlingVideoEffectsInput(
    RootModel[Union[KlingSingleImageEffectInput, KlingDualCharacterEffectInput]]
):
    root: Union[KlingSingleImageEffectInput, KlingDualCharacterEffectInput]


class KlingVideoEffectsRequest(BaseModel):
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address for the result of this task.',
    )
    effect_scene: Union[KlingDualCharacterEffectsScene, KlingSingleImageEffectsScene]
    external_task_id: Optional[str] = Field(
        None,
        description='Customized Task ID. Must be unique within a single user account.',
    )
    input: KlingVideoEffectsInput


class Data5(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_info: Optional[TaskInfo] = None
    task_result: Optional[TaskResult2] = None
    task_status: Optional[KlingTaskStatus] = None
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingVideoEffectsResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data5] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class KlingVideoExtendRequest(BaseModel):
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address. Server will notify when the task status changes.',
    )
    cfg_scale: Optional[KlingVideoGenCfgScale] = Field(
        default_factory=lambda: KlingVideoGenCfgScale.model_validate(0.5)
    )
    negative_prompt: Optional[str] = Field(
        None,
        description='Negative text prompt for elements to avoid in the extended video',
        max_length=2500,
    )
    prompt: Optional[str] = Field(
        None,
        description='Positive text prompt for guiding the video extension',
        max_length=2500,
    )
    video_id: Optional[str] = Field(
        None,
        description='The ID of the video to be extended. Supports videos generated by text-to-video, image-to-video, and previous video extension operations. Cannot exceed 3 minutes total duration after extension.',
    )


class Data6(BaseModel):
    created_at: Optional[int] = Field(None, description='Task creation time')
    task_id: Optional[str] = Field(None, description='Task ID')
    task_info: Optional[TaskInfo] = None
    task_result: Optional[TaskResult2] = None
    task_status: Optional[KlingTaskStatus] = None
    updated_at: Optional[int] = Field(None, description='Task update time')


class KlingVideoExtendResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    data: Optional[Data6] = None
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')


class LumaGenerationRequest(BaseModel):
    aspect_ratio: LumaAspectRatio
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback URL of the generation, a POST request with Generation object will be sent to the callback URL when the generation is dreaming, completed, or failed',
    )
    duration: LumaVideoModelOutputDuration
    generation_type: Optional[GenerationType1] = 'video'
    keyframes: Optional[LumaKeyframes] = None
    loop: Optional[bool] = Field(None, description='Whether to loop the video')
    model: LumaVideoModel
    prompt: str = Field(..., description='The prompt of the generation')
    resolution: LumaVideoModelOutputResolution


class CharacterRef(BaseModel):
    identity0: Optional[LumaImageIdentity] = None


class LumaImageGenerationRequest(BaseModel):
    aspect_ratio: Optional[LumaAspectRatio] = '16:9'
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback URL for the generation'
    )
    character_ref: Optional[CharacterRef] = None
    generation_type: Optional[GenerationType2] = 'image'
    image_ref: Optional[List[LumaImageRef]] = None
    model: Optional[LumaImageModel] = 'photon-1'
    modify_image_ref: Optional[LumaModifyImageRef] = None
    prompt: Optional[str] = Field(None, description='The prompt of the generation')
    style_ref: Optional[List[LumaImageRef]] = None


class LumaUpscaleVideoGenerationRequest(BaseModel):
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback URL for the upscale'
    )
    generation_type: Optional[GenerationType3] = 'upscale_video'
    resolution: Optional[LumaVideoModelOutputResolution] = None


class OutputContent(RootModel[Union[OutputTextContent, OutputAudioContent]]):
    root: Union[OutputTextContent, OutputAudioContent]


class OutputMessage(BaseModel):
    content: List[OutputContent] = Field(..., description='The content of the message')
    role: Role4 = Field(..., description='The role of the message')
    type: Type14 = Field(..., description='The type of output item')


class PikaBodyGenerate22I2vGenerate22I2vPost(BaseModel):
    duration: Optional[PikaDurationEnum] = 5
    image: Optional[StrictBytes] = Field(None, title='Image')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    promptText: Optional[str] = Field(None, title='Prompttext')
    resolution: Optional[PikaResolutionEnum] = '1080p'
    seed: Optional[int] = Field(None, title='Seed')


class PikaBodyGenerate22KeyframeGenerate22PikaframesPost(BaseModel):
    duration: Optional[int] = Field(None, ge=5, le=10, title='Duration')
    keyFrames: Optional[List[StrictBytes]] = Field(
        None, description='Array of keyframe images', title='Keyframes'
    )
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    promptText: str = Field(..., title='Prompttext')
    resolution: Optional[PikaResolutionEnum] = '1080p'
    seed: Optional[int] = Field(None, title='Seed')


class PikaBodyGenerate22T2vGenerate22T2vPost(BaseModel):
    aspectRatio: Optional[float] = Field(
        1.7777777777777777,
        description='Aspect ratio (width / height)',
        ge=0.4,
        le=2.5,
        title='Aspectratio',
    )
    duration: Optional[PikaDurationEnum] = 5
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    promptText: str = Field(..., title='Prompttext')
    resolution: Optional[PikaResolutionEnum] = '1080p'
    seed: Optional[int] = Field(None, title='Seed')


class PikaBodyGeneratePikaffectsGeneratePikaffectsPost(BaseModel):
    image: Optional[StrictBytes] = Field(None, title='Image')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    pikaffect: Optional[Pikaffect] = None
    promptText: Optional[str] = Field(None, title='Prompttext')
    seed: Optional[int] = Field(None, title='Seed')


class PikaHTTPValidationError(BaseModel):
    detail: Optional[List[PikaValidationError]] = Field(None, title='Detail')


class Reasoning(BaseModel):
    effort: Optional[ReasoningEffort] = 'medium'
    generate_summary: Optional[GenerateSummary] = Field(
        None,
        description="**Deprecated:** use `summary` instead.\n\nA summary of the reasoning performed by the model. This can be\nuseful for debugging and understanding the model's reasoning process.\nOne of `auto`, `concise`, or `detailed`.\n",
    )
    summary: Optional[Summary] = Field(
        None,
        description="A summary of the reasoning performed by the model. This can be\nuseful for debugging and understanding the model's reasoning process.\nOne of `auto`, `concise`, or `detailed`.\n",
    )


class ResponseError(BaseModel):
    code: ResponseErrorCode
    message: str = Field(..., description='A human-readable description of the error.')


class Rodin3DDownloadResponse(BaseModel):
    list: Optional[RodinResourceItem] = None


class Rodin3DGenerateRequest(BaseModel):
    images: str = Field(..., description='The reference images to generate 3D Assets.')
    material: Optional[RodinMaterialType] = None
    mesh_mode: Optional[RodinMeshModeType] = None
    quality: Optional[RodinQualityType] = None
    seed: Optional[int] = Field(None, description='Seed.')
    tier: Optional[RodinTierType] = None


class Rodin3DGenerateResponse(BaseModel):
    jobs: Optional[RodinGenerateJobsData] = None
    message: Optional[str] = Field(None, description='message')
    prompt: Optional[str] = Field(None, description='prompt')
    submit_time: Optional[str] = Field(None, description='Time')
    uuid: Optional[str] = Field(None, description='Task UUID')


class RunwayImageToVideoRequest(BaseModel):
    duration: RunwayDurationEnum
    model: RunwayModelEnum
    promptImage: RunwayPromptImageObject
    promptText: Optional[str] = Field(
        None, description='Text prompt for the generation', max_length=1000
    )
    ratio: RunwayAspectRatioEnum
    seed: int = Field(
        ..., description='Random seed for generation', ge=0, le=4294967295
    )


class TextResponseFormatConfiguration(
    RootModel[
        Union[
            ResponseFormatText, TextResponseFormatJsonSchema, ResponseFormatJsonObject
        ]
    ]
):
    root: Union[
        ResponseFormatText, TextResponseFormatJsonSchema, ResponseFormatJsonObject
    ] = Field(
        ...,
        description='An object specifying the format that the model must output.\n\nConfiguring `{ "type": "json_schema" }` enables Structured Outputs, \nwhich ensures the model will match your supplied JSON schema. Learn more in the \n[Structured Outputs guide](/docs/guides/structured-outputs).\n\nThe default format is `{ "type": "text" }` with no additional options.\n\n**Not recommended for gpt-4o and newer models:**\n\nSetting to `{ "type": "json_object" }` enables the older JSON mode, which\nensures the message the model generates is valid JSON. Using `json_schema`\nis preferred for models that support it.\n',
    )


class Tool(
    RootModel[
        Union[
            FileSearchTool, FunctionTool, WebSearchPreviewTool, ComputerUsePreviewTool
        ]
    ]
):
    root: Union[
        FileSearchTool, FunctionTool, WebSearchPreviewTool, ComputerUsePreviewTool
    ] = Field(..., discriminator='type')


class EasyInputMessage(BaseModel):
    content: Union[str, InputMessageContentList] = Field(
        ...,
        description='Text, image, or audio input to the model, used to generate a response.\nCan also contain previous assistant responses.\n',
    )
    role: Role = Field(
        ...,
        description='The role of the message input. One of `user`, `assistant`, `system`, or\n`developer`.\n',
    )
    type: Optional[Type2] = Field(
        None, description='The type of the message input. Always `message`.\n'
    )


class GeminiContent(BaseModel):
    parts: List[GeminiPart]
    role: Role1 = Field(..., examples=['user'])


class GeminiGenerateContentRequest(BaseModel):
    contents: List[GeminiContent]
    generationConfig: Optional[GeminiGenerationConfig] = None
    safetySettings: Optional[List[GeminiSafetySetting]] = None
    systemInstruction: Optional[GeminiSystemInstructionContent] = None
    tools: Optional[List[GeminiTool]] = None
    videoMetadata: Optional[GeminiVideoMetadata] = None


class ImagenGenerateImageRequest(BaseModel):
    instances: List[ImagenImageGenerationInstance]
    parameters: ImagenImageGenerationParameters


class InputMessage(BaseModel):
    content: Optional[InputMessageContentList] = None
    role: Optional[Role3] = None
    status: Optional[Status2] = None
    type: Optional[Type9] = None


class Item(
    RootModel[
        Union[
            InputMessage,
            OutputMessage,
            FileSearchToolCall,
            ComputerToolCall,
            WebSearchToolCall,
            FunctionToolCall,
            ReasoningItem,
        ]
    ]
):
    root: Union[
        InputMessage,
        OutputMessage,
        FileSearchToolCall,
        ComputerToolCall,
        WebSearchToolCall,
        FunctionToolCall,
        ReasoningItem,
    ] = Field(..., description='Content item used to generate a response.\n')


class LumaGeneration(BaseModel):
    assets: Optional[LumaAssets] = None
    created_at: Optional[datetime] = Field(
        None, description='The date and time when the generation was created'
    )
    failure_reason: Optional[str] = Field(
        None, description='The reason for the state of the generation'
    )
    generation_type: Optional[LumaGenerationType] = None
    id: Optional[UUID] = Field(None, description='The ID of the generation')
    model: Optional[str] = Field(None, description='The model used for the generation')
    request: Optional[
        Union[
            LumaGenerationRequest,
            LumaImageGenerationRequest,
            LumaUpscaleVideoGenerationRequest,
            LumaAudioGenerationRequest,
        ]
    ] = Field(None, description='The request of the generation')
    state: Optional[LumaState] = None


class OutputItem(
    RootModel[
        Union[
            OutputMessage,
            FileSearchToolCall,
            FunctionToolCall,
            WebSearchToolCall,
            ComputerToolCall,
            ReasoningItem,
        ]
    ]
):
    root: Union[
        OutputMessage,
        FileSearchToolCall,
        FunctionToolCall,
        WebSearchToolCall,
        ComputerToolCall,
        ReasoningItem,
    ]


class Text(BaseModel):
    format: Optional[TextResponseFormatConfiguration] = None


class ResponseProperties(BaseModel):
    instructions: Optional[str] = Field(
        None,
        description="Inserts a system (or developer) message as the first item in the model's context.\n\nWhen using along with `previous_response_id`, the instructions from a previous\nresponse will not be carried over to the next response. This makes it simple\nto swap out system (or developer) messages in new responses.\n",
    )
    max_output_tokens: Optional[int] = Field(
        None,
        description='An upper bound for the number of tokens that can be generated for a response, including visible output tokens and [reasoning tokens](/docs/guides/reasoning).\n',
    )
    model: Optional[OpenAIModels] = None
    previous_response_id: Optional[str] = Field(
        None,
        description='The unique ID of the previous response to the model. Use this to\ncreate multi-turn conversations. Learn more about \n[conversation state](/docs/guides/conversation-state).\n',
    )
    reasoning: Optional[Reasoning] = None
    text: Optional[Text] = None
    tool_choice: Optional[
        Union[ToolChoiceOptions, ToolChoiceTypes, ToolChoiceFunction]
    ] = Field(
        None,
        description='How the model should select which tool (or tools) to use when generating\na response. See the `tools` parameter to see how to specify which tools\nthe model can call.\n',
    )
    tools: Optional[List[Tool]] = None
    truncation: Optional[Truncation1] = Field(
        'disabled',
        description="The truncation strategy to use for the model response.\n- `auto`: If the context of this response and previous ones exceeds\n  the model's context window size, the model will truncate the \n  response to fit the context window by dropping input items in the\n  middle of the conversation. \n- `disabled` (default): If a model response will exceed the context window \n  size for a model, the request will fail with a 400 error.\n",
    )


class GeminiCandidate(BaseModel):
    citationMetadata: Optional[GeminiCitationMetadata] = None
    content: Optional[GeminiContent] = None
    finishReason: Optional[str] = None
    safetyRatings: Optional[List[GeminiSafetyRating]] = None


class GeminiGenerateContentResponse(BaseModel):
    candidates: Optional[List[GeminiCandidate]] = None
    promptFeedback: Optional[GeminiPromptFeedback] = None


class InputItem(RootModel[Union[EasyInputMessage, Item]]):
    root: Union[EasyInputMessage, Item]


class OpenAICreateResponse(CreateModelResponseProperties, ResponseProperties):
    include: Optional[List[Includable]] = Field(
        None,
        description='Specify additional output data to include in the model response. Currently\nsupported values are:\n- `file_search_call.results`: Include the search results of\n  the file search tool call.\n- `message.input_image.image_url`: Include image urls from the input message.\n- `computer_call_output.output.image_url`: Include image urls from the computer call output.\n',
    )
    input: Union[str, List[InputItem]] = Field(
        ...,
        description='Text, image, or file inputs to the model, used to generate a response.\n\nLearn more:\n- [Text inputs and outputs](/docs/guides/text)\n- [Image inputs](/docs/guides/images)\n- [File inputs](/docs/guides/pdf-files)\n- [Conversation state](/docs/guides/conversation-state)\n- [Function calling](/docs/guides/function-calling)\n',
    )
    parallel_tool_calls: Optional[bool] = Field(
        True, description='Whether to allow the model to run tool calls in parallel.\n'
    )
    store: Optional[bool] = Field(
        True,
        description='Whether to store the generated model response for later retrieval via\nAPI.\n',
    )
    stream: Optional[bool] = Field(
        False,
        description='If set to true, the model response data will be streamed to the client\nas it is generated using [server-sent events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events#Event_stream_format).\nSee the [Streaming section below](/docs/api-reference/responses-streaming)\nfor more information.\n',
    )
    usage: Optional[ResponseUsage] = None


class OpenAIResponse(ModelResponseProperties, ResponseProperties):
    created_at: Optional[float] = Field(
        None,
        description='Unix timestamp (in seconds) of when this Response was created.',
    )
    error: Optional[ResponseError] = None
    id: Optional[str] = Field(None, description='Unique identifier for this Response.')
    incomplete_details: Optional[IncompleteDetails] = Field(
        None, description='Details about why the response is incomplete.\n'
    )
    object: Optional[Object] = Field(
        None, description='The object type of this resource - always set to `response`.'
    )
    output: Optional[List[OutputItem]] = Field(
        None,
        description="An array of content items generated by the model.\n\n- The length and order of items in the `output` array is dependent\n  on the model's response.\n- Rather than accessing the first item in the `output` array and \n  assuming it's an `assistant` message with the content generated by\n  the model, you might consider using the `output_text` property where\n  supported in SDKs.\n",
    )
    output_text: Optional[str] = Field(
        None,
        description='SDK-only convenience property that contains the aggregated text output \nfrom all `output_text` items in the `output` array, if any are present. \nSupported in the Python and JavaScript SDKs.\n',
    )
    parallel_tool_calls: Optional[bool] = Field(
        True, description='Whether to allow the model to run tool calls in parallel.\n'
    )
    status: Optional[Status6] = Field(
        None,
        description='The status of the response generation. One of `completed`, `failed`, `in_progress`, or `incomplete`.',
    )
    usage: Optional[ResponseUsage] = None
